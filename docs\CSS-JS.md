# Shopify Liquid 组件 CSS/JS 分离和引用方法

## 📋 概述
本文档详细说明如何将 Shopify Liquid 组件中的内联 CSS 和 JavaScript 分离到独立文件，并正确引用。

## 🎯 分离的好处
- **代码组织**: 更清晰的文件结构
- **可维护性**: 独立文件更易于维护和调试
- **性能优化**: 浏览器可以缓存外部文件
- **代码复用**: 多个组件可以共享样式和脚本
- **开发效率**: 减少 Liquid 文件的复杂度

## 📁 文件结构
```
shopify-theme/
├── assets/
│   ├── component-name.css      # 组件样式文件
│   └── component-name.js       # 组件脚本文件
└── sections/
    └── component-name.liquid   # 组件模板文件
```

## 🔧 分离步骤

### 1. 创建 CSS 文件
**位置**: `assets/组件名.css`

**步骤**:
1. 在 `assets` 文件夹创建 CSS 文件
2. 从 Liquid 文件复制 `<style>` 标签内的所有 CSS 代码
3. 移除 `<style>` 和 `</style>` 标签
4. 保存文件

**示例**:
```css
/* assets/ir3-batch-printing-video.css */
.ir3-batch-printing-video {
  position: relative;
  background: #ffffff;
  padding: 120px 0;
  height: 100vh;
}

/* 其他样式... */
```

### 2. 创建 JavaScript 文件
**位置**: `assets/组件名.js`

**步骤**:
1. 在 `assets` 文件夹创建 JS 文件
2. 从 Liquid 文件复制 `<script>` 标签内的所有 JavaScript 代码
3. 移除 `<script>` 和 `</script>` 标签
4. 保存文件

**示例**:
```javascript
// assets/ir3-batch-printing-video.js
document.addEventListener('DOMContentLoaded', function() {
  const batchSection = document.querySelector('.ir3-batch-printing-video');
  // 其他脚本...
});
```

### 3. 更新 Liquid 文件引用

#### CSS 引用
**替换前**:
```liquid
<style>
/* 大量 CSS 代码 */
</style>
```

**替换后**:
```liquid
{{ 'ir3-batch-printing-video.css' | asset_url | stylesheet_tag }}
```

#### JavaScript 引用
**替换前**:
```liquid
<script>
// 大量 JavaScript 代码
</script>
```

**替换后**:
```liquid
<script src="{{ 'ir3-batch-printing-video.js' | asset_url }}" defer></script>
```

## 📝 引用语法详解

### CSS 引用语法
```liquid
{{ '文件名.css' | asset_url | stylesheet_tag }}
```

**解释**:
- `'文件名.css'`: assets 文件夹中的 CSS 文件名
- `asset_url`: Shopify 过滤器，生成正确的资源 URL
- `stylesheet_tag`: 生成完整的 `<link>` 标签

**生成的 HTML**:
```html
<link href="/assets/文件名.css?v=版本号" rel="stylesheet" type="text/css" media="all">
```

### JavaScript 引用语法
```liquid
<script src="{{ '文件名.js' | asset_url }}" defer></script>
```

**解释**:
- `'文件名.js'`: assets 文件夹中的 JS 文件名
- `asset_url`: 生成正确的资源 URL
- `defer`: 延迟执行，确保 DOM 加载完成后执行

**生成的 HTML**:
```html
<script src="/assets/文件名.js?v=版本号" defer></script>
```

## ⚡ 高级引用选项

### 条件加载
```liquid
{% if section.settings.enable_animations %}
  {{ 'component-animations.js' | asset_url | script_tag }}
{% endif %}
```

### 异步加载
```liquid
<script src="{{ 'component.js' | asset_url }}" async></script>
```

### 内联关键 CSS
```liquid
<style>
  /* 关键样式保持内联 */
  .component { display: block; }
</style>
{{ 'component-full.css' | asset_url | stylesheet_tag }}
```

## 🎨 命名规范

### 文件命名
- **CSS**: `组件名.css` 或 `组件名-styles.css`
- **JS**: `组件名.js` 或 `组件名-scripts.js`

### 示例
```
ir3-batch-printing-video.css
ir3-batch-printing-video.js
hero-section.css
hero-section.js
product-gallery-styles.css
product-gallery-scripts.js
```

## 🔍 调试技巧

### 检查文件加载
1. 打开浏览器开发者工具
2. 查看 Network 标签
3. 确认 CSS/JS 文件正确加载
4. 检查是否有 404 错误

### 常见问题
1. **文件路径错误**: 确保文件在 `assets` 文件夹中
2. **缓存问题**: 清除浏览器缓存或使用硬刷新
3. **语法错误**: 检查 CSS/JS 语法是否正确

## 📊 性能优化

### CSS 优化
- 移除未使用的样式
- 使用 CSS 压缩
- 合并相关的 CSS 文件

### JavaScript 优化
- 使用 `defer` 或 `async` 属性
- 压缩 JavaScript 代码
- 避免阻塞渲染的脚本

## 🎯 最佳实践

1. **保持文件小而专注**: 每个文件只负责一个组件
2. **使用语义化命名**: 文件名应该清楚表达用途
3. **添加注释**: 在文件顶部添加说明注释
4. **版本控制**: 使用 Git 跟踪文件变更
5. **测试**: 分离后充分测试功能是否正常

## 📋 检查清单

分离完成后检查：
- [ ] CSS 文件已创建并包含所有样式
- [ ] JavaScript 文件已创建并包含所有脚本
- [ ] Liquid 文件已更新引用
- [ ] 页面样式显示正常
- [ ] JavaScript 功能工作正常
- [ ] 浏览器控制台无错误
- [ ] 文件命名符合规范

## 🔄 维护流程

1. **修改样式**: 直接编辑 CSS 文件
2. **修改脚本**: 直接编辑 JS 文件
3. **添加新功能**: 在对应文件中添加代码
4. **测试**: 确保修改不影响现有功能
5. **部署**: 上传修改的文件到 Shopify

通过这种方式，你可以高效地管理和维护 Shopify 主题的样式和脚本文件！
