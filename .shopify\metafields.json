{"article": [], "blog": [], "collection": [{"key": "custom_test_product", "namespace": "custom", "name": "custom.test_product", "description": "custom.test_product", "type": {"name": "product_reference", "category": "REFERENCE"}}], "company": [], "company_location": [], "location": [], "market": [], "order": [], "page": [], "product": [{"key": "color-pattern", "namespace": "shopify", "name": "颜色", "description": "定义主要颜色或图案，例如蓝色或带条纹", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "connection-type", "namespace": "shopify", "name": "连接类型", "description": "指定电子产品使用的连接类型，例如有线或蓝牙", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "compatible-filament", "namespace": "shopify", "name": "适用灯丝", "description": "指定 3D 打印机可以使用的线材类型，例如聚乳酸 (PLA) 或尼龙", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "enclosure-type", "namespace": "shopify", "name": "封装类型", "description": "指定 3D 打印机外壳或盖子的类型，例如开放式框架或 DIY 封闭", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "print-technology", "namespace": "shopify", "name": "印刷技术", "description": "指定打印机使用的流程，例如喷墨打印机或激光打印机", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}], "variant": [], "shop": []}