

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-575067809080410911.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-575067809080410911.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-575067809080410911.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-575067809080410911.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-575067809080410911.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-575067809080410911.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-575067809080410911.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-575067809080410911.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-575067809080410911.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-575067809080410911.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-575067809080410911.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-575067809080410911.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-575067809080410911.gps.gpsil [style*="--bbw:"]{border-bottom-width:var(--bbw)}.gps-575067809080410911.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-575067809080410911.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-575067809080410911.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-575067809080410911.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-575067809080410911.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-575067809080410911.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-575067809080410911.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-575067809080410911.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-575067809080410911.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-575067809080410911.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-575067809080410911.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-575067809080410911.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-575067809080410911.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-575067809080410911.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-575067809080410911.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-575067809080410911.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-575067809080410911.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-575067809080410911.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-575067809080410911.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-575067809080410911.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-575067809080410911.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-575067809080410911.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-575067809080410911.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-575067809080410911.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-575067809080410911.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-575067809080410911.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-575067809080410911.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-575067809080410911.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-575067809080410911.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-575067809080410911.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-575067809080410911.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-575067809080410911.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-575067809080410911.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-575067809080410911.gps.gpsil [style*="--bbw-tablet:"]{border-bottom-width:var(--bbw-tablet)}.gps-575067809080410911.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-575067809080410911.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-575067809080410911.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-575067809080410911.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-575067809080410911.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-575067809080410911.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-575067809080410911.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-575067809080410911.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-575067809080410911.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-575067809080410911.gps.gpsil [style*="--bbw-mobile:"]{border-bottom-width:var(--bbw-mobile)}.gps-575067809080410911.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-575067809080410911.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-575067809080410911.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-575067809080410911.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-575067809080410911.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-575067809080410911.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-575067809080410911.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-575067809080410911.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-575067809080410911.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-575067809080410911.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-575067809080410911.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-575067809080410911.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-575067809080410911 .focus\:\!gp-shadow-none,.gps-575067809080410911 .hover\:\!gp-shadow-none{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-575067809080410911 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-575067809080410911 .gp-fixed{position:fixed}.gps-575067809080410911 .gp-relative{position:relative}.gps-575067809080410911 .gp-top-\[27px\]{top:27px}.gps-575067809080410911 .gp-z-1{z-index:1}.gps-575067809080410911 .gp-z-100{z-index:100}.gps-575067809080410911 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-575067809080410911 .gp-mb-0{margin-bottom:0}.gps-575067809080410911 .gp-block{display:block}.gps-575067809080410911 .gp-flex{display:flex}.gps-575067809080410911 .gp-inline-flex{display:inline-flex}.gps-575067809080410911 .gp-grid{display:grid}.gps-575067809080410911 .gp-contents{display:contents}.gps-575067809080410911 .\!gp-hidden{display:none!important}.gps-575067809080410911 .gp-hidden{display:none}.gps-575067809080410911 .\!gp-h-auto{height:auto!important}.gps-575067809080410911 .gp-h-auto{height:auto}.gps-575067809080410911 .gp-h-full{height:100%}.gps-575067809080410911 .gp-w-full{width:100%}.gps-575067809080410911 .gp-max-w-full{max-width:100%}.gps-575067809080410911 .gp-flex-none{flex:none}.gps-575067809080410911 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-575067809080410911 .gp-flex-col{flex-direction:column}.gps-575067809080410911 .gp-items-center{align-items:center}.gps-575067809080410911 .gp-justify-start{justify-content:flex-start}.gps-575067809080410911 .gp-justify-center{justify-content:center}.gps-575067809080410911 .gp-gap-y-0{row-gap:0}.gps-575067809080410911 .gp-overflow-hidden{overflow:hidden}.gps-575067809080410911 .gp-break-words{overflow-wrap:break-word}.gps-575067809080410911 .gp-rounded{border-radius:4px}.gps-575067809080410911 .gp-rounded-none{border-radius:0}.gps-575067809080410911 .\!gp-bg-none{background-image:none!important}.gps-575067809080410911 .gp-p-\[16px\]{padding:16px}.gps-575067809080410911 .gp-text-\[14px\]{font-size:14px}.gps-575067809080410911 .gp-leading-normal{line-height:1.5}.gps-575067809080410911 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-575067809080410911 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-575067809080410911 .gp-no-underline{text-decoration-line:none}.gps-575067809080410911 .\!gp-outline-none{outline:2px solid transparent!important;outline-offset:2px!important}.gps-575067809080410911 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-575067809080410911 .gp-duration-200{transition-duration:.2s}.gps-575067809080410911 .gp-duration-300{transition-duration:.3s}.gps-575067809080410911 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-575067809080410911 .disabled\:gp-btn-disabled:disabled{cursor:default}@media (hover:hover) and (pointer:fine){.gps-575067809080410911 .hover\:\!gp-shadow-none:hover{--tw-shadow:0 0 #0000!important;--tw-shadow-colored:0 0 #0000!important;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)!important}}.gps-575067809080410911 .focus\:\!gp-shadow-none:focus{--tw-shadow:0 0 #0000!important;--tw-shadow-colored:0 0 #0000!important;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)!important}.gps-575067809080410911 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-575067809080410911 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-575067809080410911 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-575067809080410911 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-575067809080410911 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-575067809080410911 .tablet\:gp-block{display:block}.gps-575067809080410911 .tablet\:\!gp-hidden{display:none!important}.gps-575067809080410911 .tablet\:gp-hidden{display:none}.gps-575067809080410911 .tablet\:gp-h-auto{height:auto}.gps-575067809080410911 .tablet\:gp-flex-none{flex:none}}@media (max-width:767px){.gps-575067809080410911 .mobile\:gp-block{display:block}.gps-575067809080410911 .mobile\:\!gp-hidden{display:none!important}.gps-575067809080410911 .mobile\:gp-hidden{display:none}.gps-575067809080410911 .mobile\:gp-h-auto{height:auto}.gps-575067809080410911 .mobile\:gp-flex-none{flex:none}.gps-575067809080410911 .mobile\:gp-justify-center{justify-content:center}}.gps-575067809080410911 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-575067809080410911 .\[\&_p\]\:gp-inline p{display:inline}.gps-575067809080410911 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-575067809080410911 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-575067809080410911 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gSlTEwPXLC" data-id="gSlTEwPXLC"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:50px;--pb:auto;--pt-mobile:var(--g-s-4xl);--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--pt-tablet:var(--g-s-4xl);--pl-tablet:0px;--pb-tablet:var(--g-s-4xl);--pr-tablet:0px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gSlTEwPXLC gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="guxdj07AEN gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gY3ugW4-cU" data-id="gY3ugW4-cU"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-4xl);--pl:15px;--pr:15px;--cg:15px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gY3ugW4-cU gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gbdckULm91 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gRjES7hw5G">
    <div
      parentTag="Col"
        class="gRjES7hw5G "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s)"
      >
      <div class="gp-flex" style="--jc:center">
        <h3
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:28px;--size-tablet:28px;--size-mobile:24px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggRjES7hw5G_text | replace: '$locationOrigin', locationOrigin }}</h3>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gNdY1_0cjj">
    <div
      parentTag="Col"
        class="gNdY1_0cjj "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-2xl)"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggNdY1_0cjj_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
       
      
    <div
      parentTag="Col" id="gryl6nTiCs" data-id="gryl6nTiCs"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb-mobile:var(--g-s-2xl);--cg:var(--g-s-l);--pc:center;--gtc:minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gryl6nTiCs gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="g8ZCNl6zaJ gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="ggln1KsjFO"
    role="presentation"
    class="gp-group/image ggln1KsjFO gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:center"
    >
      <a
        class="pointer-events-auto gp-h-full gp-flex"
        href="#" target="_self" title="Image Title"
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-c52d14cd-3392-4b6f-83e1-8f11e8b17903.png" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQ0IiBoZWlnaHQ9IjE0NCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctMTQ0LTE0NCI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iMTQ0IiBoZWlnaHQ9IjE0NCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTQ0IiBoZWlnaHQ9IjE0NCIgZmlsbD0idXJsKCNnLTE0NC0xNDQpIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii0xNDQiIHRvPSIxNDQiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-c52d14cd-3392-4b6f-83e1-8f11e8b17903.png" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQ0IiBoZWlnaHQ9IjE0NCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctMTQ0LTE0NCI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iMTQ0IiBoZWlnaHQ9IjE0NCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTQ0IiBoZWlnaHQ9IjE0NCIgZmlsbD0idXJsKCNnLTE0NC0xNDQpIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii0xNDQiIHRvPSIxNDQiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQ0IiBoZWlnaHQ9IjE0NCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctMTQ0LTE0NCI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iMTQ0IiBoZWlnaHQ9IjE0NCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTQ0IiBoZWlnaHQ9IjE0NCIgZmlsbD0idXJsKCNnLTE0NC0xNDQpIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii0xNDQiIHRvPSIxNDQiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="{{ "gempages_572751041980793671-c52d14cd-3392-4b6f-83e1-8f11e8b17903.png" | file_url }}"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:48px;--w-tablet:48px;--w-mobile:32px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </a>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gZrQjIej2_ gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="ggz1fj_zaE"
    role="presentation"
    class="gp-group/image ggz1fj_zaE gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:center"
    >
      <a
        class="pointer-events-auto gp-h-full gp-flex"
        href="#" target="_self" title="Image Title"
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-25e4be84-c40a-4aa6-9edc-8bcdd9ef944b.png" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQ0IiBoZWlnaHQ9IjE0NCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctMTQ0LTE0NCI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iMTQ0IiBoZWlnaHQ9IjE0NCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTQ0IiBoZWlnaHQ9IjE0NCIgZmlsbD0idXJsKCNnLTE0NC0xNDQpIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii0xNDQiIHRvPSIxNDQiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-25e4be84-c40a-4aa6-9edc-8bcdd9ef944b.png" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQ0IiBoZWlnaHQ9IjE0NCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctMTQ0LTE0NCI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iMTQ0IiBoZWlnaHQ9IjE0NCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTQ0IiBoZWlnaHQ9IjE0NCIgZmlsbD0idXJsKCNnLTE0NC0xNDQpIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii0xNDQiIHRvPSIxNDQiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQ0IiBoZWlnaHQ9IjE0NCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctMTQ0LTE0NCI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iMTQ0IiBoZWlnaHQ9IjE0NCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTQ0IiBoZWlnaHQ9IjE0NCIgZmlsbD0idXJsKCNnLTE0NC0xNDQpIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii0xNDQiIHRvPSIxNDQiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="{{ "gempages_572751041980793671-25e4be84-c40a-4aa6-9edc-8bcdd9ef944b.png" | file_url }}"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:48px;--w-tablet:48px;--w-mobile:32px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </a>
  </div>
    </div>
    </div>
   
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gOq1-dI5U2 gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:none;--d-mobile:block;--d-tablet:none;--op:100%;--pt:16px;--pb:16px;--mb-mobile:var(--g-s-2xl);--pt-mobile:0px;--pb-mobile:0px" class="gj9EqjXYQV ">
      
    <div
    data-id="gj9EqjXYQV"
      class="gp-flex gp-justify-start mobile:gp-justify-center"
    >
      <div
        style="--w:100px;--w-tablet:100px;--w-mobile:50%;--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--t:gp-rotate(0deg);--bc:#EEEEEE"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100px;--w-tablet:100px;--w-mobile:50%;--bc:#EEEEEE;--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100px;--w-tablet:100px;--w-mobile:50%;--bc:#EEEEEE;--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--minw:100px;--minw-mobile:50%"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100px;--w-tablet:100px;--w-mobile:50%;--bc:#EEEEEE;--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--minw:100px;--minw-mobile:50%"
    ></div>
  
      </div>
    </div>
  
      </div>
       
      
    <div
      parentTag="Col" id="giKJw-K48R" data-id="giKJw-K48R"
        style="--bs:solid;--bw:0px 0px 0px 1px;--bc:#EEEEEE;--shadow:none;--d:grid;--d-mobile:none;--d-tablet:grid;--op:100%;--pl:0px;--pl-tablet:15px;--pr-tablet:15px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="giKJw-K48R gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g59yBcOoDy gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g_fdmNCFuK">
    <div
      parentTag="Col"
        class="g_fdmNCFuK "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s)"
      >
      <div class="gp-flex" style="--jc:center">
        <h3
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:28px;--size-tablet:28px;--size-mobile:24px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg_fdmNCFuK_text | replace: '$locationOrigin', locationOrigin }}</h3>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g3CUbZIwBA">
    <div
      parentTag="Col"
        class="g3CUbZIwBA "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-2xl)"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg3CUbZIwBA_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pt:auto;--pl:0px;--pb:auto;--pr:0px" class="gbT4uXVVgq ">
      
    <gp-form
      id="gbT4uXVVgq"
      data-id="gbT4uXVVgq"
      
      data-submit-action=''
      data-callback='{"link":"","target":"_self"}'
    >
      <a
        hidden
        id="gp-form-callback-gbT4uXVVgq"
        href="" target=""
      >
      </a>
      {% form 'customer', class: 'gp-form-gbT4uXVVgq ', id: 'contact_form_gbT4uXVVgq' %}
        {% if form.errors %}
          <div
            id="gp-form-error-gbT4uXVVgq"
            class="gp-form-message gp-hidden gp-top-[27px] gp-z-100 gp-fixed gp-rounded gp-p-[16px] gp-text-[14px] gp-leading-normal"
            style="background-color:#FFE9E9;left:50%;transform:translateX(-50%);color:#EA3335"
          >
            {{ section.settings.ggbT4uXVVgq_errorMessage }}
          </div>
        {% endif %}
        <div popover id="my-popover-gbT4uXVVgq">
        <style>
            #my-popover-gbT4uXVVgq::backdrop {
              width: fit-content;
              height: fit-content;
            }
        </style>
        <div
          id="gp-form-success-gbT4uXVVgq"
          class="gp-form-message gp-hidden gp-top-[27px] gp-z-100 gp-fixed gp-rounded gp-p-[16px] gp-text-[14px] gp-leading-normal"
          style="background-color:#F2FFEC;left:50%;transform:translateX(-50%);color:#52C41A"
        >
          {{ section.settings.ggbT4uXVVgq_successMessage }}
        </div></div>
        
       
      
    <div
      parentTag="Newsletter" id="gU2WXByb1w" data-id="gU2WXByb1w"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:0px;--cg:0px;--pc:start;--gtc:minmax(0, 8fr) minmax(0, 4fr);--gtc-mobile:minmax(0, 7fr) minmax(0, 5fr);--w:430px;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gU2WXByb1w gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gyLrz95D7N gp-relative gp-flex gp-flex-col"
    >
      
    <div
    data-id="gBHTDfBgtd"
      class="gBHTDfBgtd"
      style="--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    >
      <input
        type="email"
        class="gp-form-item gp-g-paragraph-1 !gp-outline-none !gp-h-auto focus:!gp-shadow-none hover:!gp-shadow-none !gp-bg-none"
        style="--pl:32px;--pr:32px;--pt:11px;--pb:11px;--pl-mobile:32px;--pr-mobile:32px;--pt-mobile:12.5px;--pb-mobile:12.5px;--w:100%;--w-tablet:100%;--w-mobile:313px;--bs:solid;--bw:1px 1px 1px 1px;--bc:#E0E0E0;--shadow:none"
        placeholder="{{ section.settings.ggBHTDfBgtd_placeholder }}"
        {% if true %}
        required
        {% endif %}
        name="contact[email]"
        value=""
        autocomplete="email"
      ></input>
    </div>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gXBRjtHXS_ gp-relative gp-flex gp-flex-col"
    >
      
    
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    
  >
    <style>
    .gKyKQj_DYI.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: solid;
  border-width: 0px 0px 0px 0px;
  border-color: var(--g-c-brand, brand);
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .gKyKQj_DYI:hover::before {
      border-style: solid;
  border-width: 0px 0px 0px 0px;
  border-color: var(--g-c-brand, brand);
  
      
    }

    .gKyKQj_DYI:hover .gp-button-icon {
      color: undefined;
    }

     .gKyKQj_DYI .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gKyKQj_DYI:hover .gp-button-price {
      color: undefined;
    }

    .gKyKQj_DYI .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gKyKQj_DYI .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gKyKQj_DYI:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="submit" data-id="gKyKQj_DYI" aria-label="Subscribe"
      
      data-state="idle"
      class="gKyKQj_DYI gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3"
      style="--hvr-bg:#424242;--bg:#242424;--pl:32px;--pr:32px;--pt:12px;--pb:12px;--pl-tablet:32px;--pr-tablet:32px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:32px;--pr-mobile:32px;--pt-mobile:14px;--pb-mobile:14px;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:13px;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--lh:150%;--lh-tablet:150%;--lh-mobile:150%"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:13px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggKyKQj_DYI_label }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

  
    </div>
    </div>
   
    
      {% endform %}
    </gp-form>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-form.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gFgpj8qL9E" data-id="gFgpj8qL9E"
        style="--bs:solid;--bw:0px 0px 0px 0px;--bc:#EEEEEE;--shadow:none;--d:none;--d-mobile:grid;--d-tablet:none;--op:100%;--pl:15px;--pl-mobile:0px;--pl-tablet:0px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gFgpj8qL9E gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gUDTtRKxI7 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gnYx13kzCi">
    <div
      parentTag="Col"
        class="gnYx13kzCi "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s)"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:28px;--size-tablet:28px;--size-mobile:24px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggnYx13kzCi_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gogm2qI15k">
    <div
      parentTag="Col"
        class="gogm2qI15k "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-2xl)"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggogm2qI15k_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pt:auto;--pl:var(--g-s-l);--pb:auto;--pr:var(--g-s-l);--pt-mobile:auto;--pl-mobile:0px;--pb-mobile:auto;--pr-mobile:0px" class="gYEzE7iHlp ">
      
    <gp-form
      id="gYEzE7iHlp"
      data-id="gYEzE7iHlp"
      
      data-submit-action=''
      data-callback='{"link":"","target":"_self"}'
    >
      <a
        hidden
        id="gp-form-callback-gYEzE7iHlp"
        href="" target=""
      >
      </a>
      {% form 'customer', class: 'gp-form-gYEzE7iHlp ', id: 'contact_form_gYEzE7iHlp' %}
        {% if form.errors %}
          <div
            id="gp-form-error-gYEzE7iHlp"
            class="gp-form-message gp-hidden gp-top-[27px] gp-z-100 gp-fixed gp-rounded gp-p-[16px] gp-text-[14px] gp-leading-normal"
            style="background-color:#FFE9E9;left:50%;transform:translateX(-50%);color:#EA3335"
          >
            {{ section.settings.ggYEzE7iHlp_errorMessage }}
          </div>
        {% endif %}
        <div popover id="my-popover-gYEzE7iHlp">
        <style>
            #my-popover-gYEzE7iHlp::backdrop {
              width: fit-content;
              height: fit-content;
            }
        </style>
        <div
          id="gp-form-success-gYEzE7iHlp"
          class="gp-form-message gp-hidden gp-top-[27px] gp-z-100 gp-fixed gp-rounded gp-p-[16px] gp-text-[14px] gp-leading-normal"
          style="background-color:#F2FFEC;left:50%;transform:translateX(-50%);color:#52C41A"
        >
          {{ section.settings.ggYEzE7iHlp_successMessage }}
        </div></div>
        
       
      
    <div
      parentTag="Newsletter" id="guQvmhFrhA" data-id="guQvmhFrhA"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:0px;--cg:0px;--pc:start;--gtc:minmax(0, 8fr) minmax(0, 4fr);--gtc-mobile:minmax(0, 7fr) minmax(0, 5fr);--w:430px;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="guQvmhFrhA gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g0GPGXCKIL gp-relative gp-flex gp-flex-col"
    >
      
    <div
    data-id="g2SrjVGu-X"
      class="g2SrjVGu-X"
      style="--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    >
      <input
        type="email"
        class="gp-form-item gp-g-paragraph-1 !gp-outline-none !gp-h-auto focus:!gp-shadow-none hover:!gp-shadow-none !gp-bg-none"
        style="--pl:32px;--pr:32px;--pt:11px;--pb:11px;--pl-mobile:32px;--pr-mobile:32px;--pt-mobile:12.5px;--pb-mobile:12.5px;--w:100%;--w-tablet:100%;--w-mobile:313px;--bs:solid;--bw:1px 1px 1px 1px;--bc:#E0E0E0;--shadow:none"
        placeholder="{{ section.settings.gg2SrjVGu-X_placeholder }}"
        {% if true %}
        required
        {% endif %}
        name="contact[email]"
        value=""
        autocomplete="email"
      ></input>
    </div>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gghxCflctl gp-relative gp-flex gp-flex-col"
    >
      
    
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    
  >
    <style>
    .grLESZzFPv.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: solid;
  border-width: 0px 0px 0px 0px;
  border-color: var(--g-c-brand, brand);
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .grLESZzFPv:hover::before {
      border-style: solid;
  border-width: 0px 0px 0px 0px;
  border-color: var(--g-c-brand, brand);
  
      
    }

    .grLESZzFPv:hover .gp-button-icon {
      color: undefined;
    }

     .grLESZzFPv .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .grLESZzFPv:hover .gp-button-price {
      color: undefined;
    }

    .grLESZzFPv .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .grLESZzFPv .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .grLESZzFPv:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="submit" data-id="grLESZzFPv" aria-label="Subscribe"
      
      data-state="idle"
      class="grLESZzFPv gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3"
      style="--hvr-bg:#424242;--bg:#242424;--pl:32px;--pr:32px;--pt:12px;--pb:12px;--pl-tablet:32px;--pr-tablet:32px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:32px;--pr-mobile:32px;--pt-mobile:14px;--pb-mobile:14px;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:13px;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--lh:150%;--lh-tablet:150%;--lh-mobile:150%"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:13px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggrLESZzFPv_label }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

  
    </div>
    </div>
   
    
      {% endform %}
    </gp-form>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-form.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 5",
    "tag": "section",
    "class": "gps-575067809080410911 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=575053851124565104&sectionId=575067809080410911)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggRjES7hw5G_text","label":"ggRjES7hw5G_text","default":"On Facebook"},{"type":"html","id":"ggNdY1_0cjj_text","label":"ggNdY1_0cjj_text","default":"<p>Follow us for more useful information</p>"},{"type":"html","id":"gg_fdmNCFuK_text","label":"gg_fdmNCFuK_text","default":"<p>Let’s Stay In Touch</p>"},{"type":"html","id":"gg3CUbZIwBA_text","label":"gg3CUbZIwBA_text","default":"<p>We’ll shout you $10 off your first order</p>"},{"type":"html","id":"ggbT4uXVVgq_successMessage","label":"ggbT4uXVVgq_successMessage","default":"Thanks for Subscribing. Coupon code: XXX-555"},{"type":"html","id":"ggbT4uXVVgq_errorMessage","label":"ggbT4uXVVgq_errorMessage","default":"Can’t send email. Please try again later."},{"type":"html","id":"ggBHTDfBgtd_placeholder","label":"ggBHTDfBgtd_placeholder","default":"Enter your email"},{"type":"html","id":"ggKyKQj_DYI_label","label":"ggKyKQj_DYI_label","default":"Subscribe"},{"type":"html","id":"ggnYx13kzCi_text","label":"ggnYx13kzCi_text","default":"<p>Let’s Stay In Touch</p>"},{"type":"html","id":"ggogm2qI15k_text","label":"ggogm2qI15k_text","default":"<p>We’ll shout you $10 off your first order</p>"},{"type":"html","id":"ggYEzE7iHlp_successMessage","label":"ggYEzE7iHlp_successMessage","default":"Thanks for Subscribing. Coupon code: XXX-555"},{"type":"html","id":"ggYEzE7iHlp_errorMessage","label":"ggYEzE7iHlp_errorMessage","default":"Can’t send email. Please try again later."},{"type":"html","id":"gg2SrjVGu-X_placeholder","label":"gg2SrjVGu-X_placeholder","default":"Enter your email"},{"type":"html","id":"ggrLESZzFPv_label","label":"ggrLESZzFPv_label","default":"Subscribe"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
