/*
  IR3 Smart Features - Apple Style Horizontal Card Carousel
  File: assets/ir3-smart-features.css
  Clean version without conflicts
*/

/* Section Container - 与页面其他组件保持一致的深色背景 */
.smart-features-section {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f0f0f 100%);
  overflow: hidden;
  padding: 80px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Content Container */
.smart-container {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  width: 100%;
}

/* Title Section - 统一与 Hero Section 的字体样式 */
.title-section {
  text-align: center;
  margin-bottom: 60px; /* 减少上方空白 */
}

.smart-features-title {
  font-size: clamp(2.5rem, 5vw, 4.5rem); /* 与 Hero Section 一致 */
  font-weight: 900; /* 与 Hero Section 一致 */
  font-family: 'Montserrat', sans-serif;  /* 与 Hero Section 一致 */
  line-height: 1;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  position: relative;
  background: linear-gradient(135deg, #1d1d1f 0%, #42a5f5 50%, #1de9b6 100%); /* 与 Hero Section 类似的渐变 */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: clamp(1.25rem, 2.5vw, 1.875rem); /* 与 Hero Section sub-title 一致 */
  font-weight: 300; /* 与 Hero Section 一致 */
  color: rgba(255, 255, 255, 0.9); /* 深色背景下的白色文字 */
  margin: 0 auto;
  max-width: 800px;
  letter-spacing: 0.02em;
}

/* Cards Container */
.cards-carousel {
  position: relative;
  width: 100%;
}

.cards-viewport {
  position: relative;
  width: 100%;
  height: 600px; /* 减少高度，更紧凑的布局 */
  overflow: visible;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cards-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.8s ease;
}

/* Feature Cards - 现代化设计优化 */
.feature-card {
  position: absolute;
  width: 900px;
  height: 570px; /* 稍微减少高度，优化比例 */
  border-radius: 28px; /* 更圆润的边角 */
  overflow: hidden;
  background: rgba(255, 255, 255, 0.98); /* 更高的透明度 */
  backdrop-filter: blur(30px); /* 更强的模糊效果 */
  border: 1px solid rgba(255, 255, 255, 0.2); /* 添加微妙边框 */
  box-shadow:
    0 32px 100px rgba(0, 0, 0, 0.12), /* 更柔和的阴影 */
    0 16px 40px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.3); /* 内部高光 */
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
  display: flex;
  flex-direction: column;
}

/* 卡片悬停效果 */
.feature-card:hover {
  transform: translateY(-8px) scale(1.02); /* 悬停时轻微上升和放大 */
  box-shadow:
    0 40px 120px rgba(0, 0, 0, 0.15),
    0 20px 50px rgba(0, 0, 0, 0.1),
    0 8px 20px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* Card Positioning - Adjusted for larger cards */
.feature-card[data-card="0"] {
  z-index: 10;
  transform: translateX(0) scale(1);
  opacity: 1;
}

.feature-card[data-card="1"] {
  z-index: 5;
  transform: translateX(650px) scale(0.8);
  opacity: 0.5;
}

.feature-card[data-card="2"] {
  z-index: 3;
  transform: translateX(-650px) scale(0.8);
  opacity: 0.5;
}

.feature-card[data-card="3"] {
  z-index: 1;
  transform: translateX(1300px) scale(0.6);
  opacity: 0.3;
}

.feature-card[data-card="4"] {
  z-index: 1;
  transform: translateX(-1300px) scale(0.6);
  opacity: 0.3;
}

/* Card switching animations - Show Card 1 */
.cards-container.show-card-1 .feature-card[data-card="0"] {
  transform: translateX(-500px) scale(0.8);
  opacity: 0.5;
  z-index: 5;
}

.cards-container.show-card-1 .feature-card[data-card="1"] {
  transform: translateX(0) scale(1);
  opacity: 1;
  z-index: 10;
}

.cards-container.show-card-1 .feature-card[data-card="2"] {
  transform: translateX(500px) scale(0.8);
  opacity: 0.5;
  z-index: 5;
}

.cards-container.show-card-1 .feature-card[data-card="3"] {
  transform: translateX(-1000px) scale(0.6);
  opacity: 0.3;
  z-index: 3;
}

.cards-container.show-card-1 .feature-card[data-card="4"] {
  transform: translateX(1000px) scale(0.6);
  opacity: 0.3;
  z-index: 1;
}

/* Card switching animations - Show Card 2 */
.cards-container.show-card-2 .feature-card[data-card="0"] {
  transform: translateX(1000px) scale(0.6);
  opacity: 0.3;
  z-index: 1;
}

.cards-container.show-card-2 .feature-card[data-card="1"] {
  transform: translateX(-500px) scale(0.8);
  opacity: 0.5;
  z-index: 5;
}

.cards-container.show-card-2 .feature-card[data-card="2"] {
  transform: translateX(0) scale(1);
  opacity: 1;
  z-index: 10;
}

.cards-container.show-card-2 .feature-card[data-card="3"] {
  transform: translateX(500px) scale(0.8);
  opacity: 0.5;
  z-index: 5;
}

.cards-container.show-card-2 .feature-card[data-card="4"] {
  transform: translateX(-1000px) scale(0.6);
  opacity: 0.3;
  z-index: 1;
}

/* Card switching animations - Show Card 3 */
.cards-container.show-card-3 .feature-card[data-card="0"] {
  transform: translateX(-1000px) scale(0.6);
  opacity: 0.3;
  z-index: 1;
}

.cards-container.show-card-3 .feature-card[data-card="1"] {
  transform: translateX(1000px) scale(0.6);
  opacity: 0.3;
  z-index: 3;
}

.cards-container.show-card-3 .feature-card[data-card="2"] {
  transform: translateX(-500px) scale(0.8);
  opacity: 0.5;
  z-index: 5;
}

.cards-container.show-card-3 .feature-card[data-card="3"] {
  transform: translateX(0) scale(1);
  opacity: 1;
  z-index: 10;
}

.cards-container.show-card-3 .feature-card[data-card="4"] {
  transform: translateX(500px) scale(0.8);
  opacity: 0.5;
  z-index: 5;
}

/* Card switching animations - Show Card 4 */
.cards-container.show-card-4 .feature-card[data-card="0"] {
  transform: translateX(500px) scale(0.8);
  opacity: 0.5;
  z-index: 5;
}

.cards-container.show-card-4 .feature-card[data-card="1"] {
  transform: translateX(1000px) scale(0.6);
  opacity: 0.3;
  z-index: 1;
}

.cards-container.show-card-4 .feature-card[data-card="2"] {
  transform: translateX(-1000px) scale(0.6);
  opacity: 0.3;
  z-index: 1;
}

.cards-container.show-card-4 .feature-card[data-card="3"] {
  transform: translateX(-500px) scale(0.8);
  opacity: 0.5;
  z-index: 5;
}

.cards-container.show-card-4 .feature-card[data-card="4"] {
  transform: translateX(0) scale(1);
  opacity: 1;
  z-index: 10;
}

/* Video Section - 调整为 70% 以配合内容区域的 30% */
.card-video-section {
  position: relative;
  width: 100%;
  height: 70%; /* 调整为 70% 以配合内容区域 */
  overflow: hidden;
  border-radius: 28px 28px 0 0; /* 与卡片边角保持一致 */
}

.card-video-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.card-video-bg video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Minimal video overlay for better visibility */
.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.05) 50%,
    rgba(0, 0, 0, 0.2) 100%
  );
  z-index: 2;
}

/* Content Section - 优化内容区域布局 */
.card-content-section {
  position: relative;
  width: 100%;
  height: 30%; /* 增加到 30% 给文字更多空间 */
  min-height: 240px; /* 增加最小高度以确保文字不被挤压 */
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 0 0 28px 28px; /* 与卡片边角保持一致 */
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 24px 30px 20px 30px; /* 减少左右内边距，防止内容被切掉 */
  overflow: hidden;
}

.card-content {
  position: relative;
  z-index: 3;
  width: 100%;
  height: 100%;
  text-align: left;
  color: #1d1d1f;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* 顶部对齐 */
  gap: 18px; /* 增加间距，提供更好的视觉层次 */
  padding-top: 4px; /* 添加顶部内边距 */
}

.feature-card.active .card-content {
  opacity: 1;
  transform: translateY(0);
}

/* Card icon removed for cleaner Apple-style design */

.card-title {
  font-size: clamp(1.3rem, 2.2vw, 1.5rem); /* 增大标题字体，提高视觉层次 */
  font-weight: 700;
  margin: 0 0 10px 0; /* 适当的底部间距 */
  color: #1d1d1f; /* 保持深色，因为卡片背景是白色 */
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
  letter-spacing: -0.02em;
  line-height: 1.25; /* 稍微增加行高，提高可读性 */
  flex-shrink: 0;
}

.card-description {
  font-size: clamp(0.9rem, 1.3vw, 1rem); /* 增大描述字体，提高可读性 */
  line-height: 1.5; /* 增加行高，提高阅读体验 */
  margin: 0 0 16px 0; /* 增加底部间距 */
  color: #424245; /* 保持深色，因为卡片背景是白色 */
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Segoe UI', Roboto, sans-serif;
  flex-grow: 1;
}

.card-features {
  list-style: none;
  padding: 0;
  margin: 0 0 32px 0; /* 保持底部间距 */
  text-align: left; /* 改回左对齐 */
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap; /* 单行显示 */
  gap: 8px 12px; /* 减少间距以适应更多内容 */
  justify-content: flex-start; /* 改回左对齐 */
  flex-shrink: 0;
  overflow-x: auto; /* 恢复水平滚动以防止内容被切掉 */
  overflow-y: hidden;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

/* 隐藏 WebKit 滚动条 */
.card-features::-webkit-scrollbar {
  display: none;
}

/* 移除所有滚动条相关样式 */

.card-features li {
  font-size: clamp(0.75rem, 1vw, 0.85rem); /* 稍微减小字体 */
  color: #1d1d1f;
  position: relative;
  padding: 5px 10px 5px 18px; /* 减少内边距以适应更多内容 */
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Segoe UI', Roboto, sans-serif;
  line-height: 1.3;
  background: linear-gradient(135deg, rgba(66, 165, 245, 0.12) 0%, rgba(29, 233, 182, 0.1) 100%);
  border: 1px solid rgba(66, 165, 245, 0.2);
  border-radius: 14px; /* 稍微减小圆角 */
  white-space: nowrap;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  flex-shrink: 0; /* 防止收缩 */
  box-shadow: 0 2px 8px rgba(66, 165, 245, 0.08);
  font-weight: 500;
  max-width: none; /* 确保标签不会被截断 */
}

.card-features li:hover {
  background: linear-gradient(135deg, rgba(66, 165, 245, 0.18) 0%, rgba(29, 233, 182, 0.15) 100%);
  transform: translateY(-2px) scale(1.02); /* 更明显的悬停效果 */
  box-shadow: 0 4px 16px rgba(66, 165, 245, 0.15); /* 悬停时增强阴影 */
  border-color: rgba(66, 165, 245, 0.3);
}

.card-features li::before {
  content: '✓';
  position: absolute;
  left: 8px; /* 增加间距，让图标与文字有更多空间 */
  top: 50%;
  transform: translateY(-50%);
  color: #42a5f5;
  font-weight: bold;
  font-size: 0.8em; /* 稍微减小图标大小 */
  text-shadow: 0 1px 2px rgba(66, 165, 245, 0.3);
}

/* Navigation Controls - Side positioned */
.navigation-controls {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 20;
}

/* 高级现代化导航按钮设计 */
.nav-arrow {
  position: absolute;
  top: 50%; /* 垂直居中定位 */
  transform: translateY(-50%); /* 精确居中 */
  width: 64px;
  height: 64px;
  border: none;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
  color: #1d1d1f;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 4px 16px rgba(0, 0, 0, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  overflow: hidden;
  pointer-events: auto;
  z-index: 10; /* 确保按钮在最上层 */
}

/* 添加内部发光效果 */
.nav-arrow::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(66, 165, 245, 0.1) 0%, rgba(29, 233, 182, 0.08) 100%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.prev-arrow {
  left: -140px; /* 增加与卡片的距离 */
}

.next-arrow {
  right: -140px; /* 增加与卡片的距离 */
}

/* 高级悬停效果 */
.nav-arrow:hover {
  background: linear-gradient(135deg, #42a5f5 0%, #1de9b6 100%);
  color: white;
  transform: translateY(-50%) scale(1.08) translateY(-4px); /* 保持居中的同时添加悬停效果 */
  box-shadow:
    0 12px 40px rgba(66, 165, 245, 0.25),
    0 6px 20px rgba(66, 165, 245, 0.15),
    0 3px 10px rgba(66, 165, 245, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
}

.nav-arrow:hover::before {
  opacity: 1;
}

/* 点击效果 */
.nav-arrow:active {
  transform: translateY(-50%) scale(0.92) translateY(-2px); /* 保持居中的同时添加点击效果 */
  transition: transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 6px 20px rgba(66, 165, 245, 0.2),
    0 3px 10px rgba(66, 165, 245, 0.1);
}

/* 禁用状态 */
.nav-arrow:disabled {
  opacity: 0.3;
  cursor: not-allowed;
  transform: translateY(-50%); /* 保持居中定位 */
  background: rgba(255, 255, 255, 0.4);
  color: #8e8e93;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.nav-arrow:disabled:hover {
  transform: translateY(-50%); /* 保持居中定位 */
  background: rgba(255, 255, 255, 0.4);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* SVG 图标样式 */
.nav-arrow svg {
  width: 26px;
  height: 26px;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.nav-arrow:hover svg {
  transform: translateX(3px) scale(1.1);
  filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.3));
}

.prev-arrow:hover svg {
  transform: translateX(-3px) scale(1.1);
  filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.3));
}

/* Progress Indicators - 重新设计为高级样式，正确定位 */
.progress-indicators {
  position: absolute;
  bottom: -80px; /* 移到卡片下方，而不是卡片上方 */
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 16px; /* 增加间距 */
  align-items: center;
  justify-content: center; /* 确保内容居中 */
  z-index: 15;
  padding: 12px 24px; /* 添加内边距 */
  background: rgba(255, 255, 255, 0.1); /* 半透明背景 */
  backdrop-filter: blur(20px); /* 模糊效果 */
  border-radius: 50px; /* 胶囊形状 */
  border: 1px solid rgba(255, 255, 255, 0.2); /* 微妙边框 */
  width: auto; /* 确保宽度自适应 */
  min-width: fit-content; /* 最小宽度适应内容 */
}

.indicator {
  width: 8px; /* 减小尺寸，更精致 */
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4); /* 适应深色背景 */
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: linear-gradient(135deg, #42a5f5 0%, #1de9b6 100%); /* 渐变色 */
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.indicator.active {
  background: rgba(255, 255, 255, 0.8); /* 更亮的背景 */
  transform: scale(1.3); /* 更明显的放大 */
  box-shadow: 0 0 12px rgba(66, 165, 245, 0.6); /* 发光效果 */
}

.indicator.active::before {
  width: 6px;
  height: 6px;
  background: linear-gradient(135deg, #42a5f5 0%, #1de9b6 100%); /* 渐变色填充 */
}

.indicator:hover {
  background: rgba(255, 255, 255, 0.6);
  transform: scale(1.15);
  box-shadow: 0 0 8px rgba(66, 165, 245, 0.4); /* 悬停发光 */
}

/* Completion Indicator - 已移除 */

/* Completion 相关样式已全部移除 */

/* Scroll Lock Styles and Animations */
body.scroll-locked {
  overflow: hidden;
  position: fixed;
  width: 100%;
}

/* Enhanced Scroll Lock Visual Indicator */
.scroll-lock-indicator {
  position: fixed;
  top: 30px;
  right: 30px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 16px 24px;
  border-radius: 30px;
  font-size: 15px;
  font-weight: 600;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Segoe UI', Roboto, sans-serif;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  z-index: 9999;
  opacity: 0;
  transform: translateX(100px) scale(0.9);
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  pointer-events: none;
}

.scroll-lock-indicator.show {
  opacity: 1;
  transform: translateX(0) scale(1);
}

.scroll-lock-indicator::before {
  content: '🔒';
  margin-right: 10px;
  font-size: 16px;
}

/* Section Entry Animation */
.smart-features-section.entering {
  animation: sectionEnter 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes sectionEnter {
  from {
    opacity: 0.8;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Desktop Responsive Design - 确保桌面端居中 */
@media (min-width: 769px) {
  .progress-indicators {
    left: 50% !important;
    transform: translateX(-50%) !important;
    justify-content: center !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
}

/* Mobile Responsive Design - 优化移动端体验 */
@media (max-width: 768px) {
  .smart-features-section {
    padding: 50px 0; /* 减少移动端内边距 */
  }

  .smart-container {
    padding: 0 20px;
  }

  .title-section {
    margin-bottom: 40px; /* 减少标题下方空白 */
  }

  .smart-features-title {
    font-size: clamp(2rem, 6vw, 3rem); /* 移动端标题大小优化 */
  }

  .subtitle {
    font-size: clamp(1rem, 3vw, 1.2rem); /* 移动端副标题优化 */
  }

  .cards-carousel {
    overflow: visible !important; /* 确保横向滚动内容不被裁切 */
  }

  .cards-viewport {
    height: 500px; /* 增加移动端卡片容器高度 */
    overflow: visible !important; /* 确保横向滚动内容不被裁切 */
  }

  .cards-container {
    overflow: visible !important; /* 确保横向滚动内容不被裁切 */
  }

  .feature-card {
    width: 340px;
    height: 450px; /* 稍微减少移动端卡片高度，优化比例 */
    overflow: visible; /* 确保横向滚动内容不被裁切 */
  }

  .card-content-section {
    height: 320px !important; /* 增加移动端内容区域高度，确保特性列表完整显示 */
    min-height: 320px !important; /* 强制所有卡片应用最小高度，确保内容完整显示 */
    max-height: 320px !important; /* 强制最大高度，防止内容溢出 */
    padding: 20px 0px 15px 0px; /* 优化移动端内边距，减少顶部空白 */
    overflow: visible !important; /* 确保内容可见，不被隐藏 */
  }

  .card-video-section {
    height: 130px !important; /* 调整移动端视频区域高度，与内容区域320px配合 */
    min-height: 130px !important; /* 确保视频区域最小高度 */
    max-height: 130px !important; /* 限制视频区域最大高度 */
  }

  .card-title {
    font-size: clamp(1.2rem, 4vw, 1.4rem); /* 移动端标题大小 */
    margin: 0 0 8px 0; /* 减少标题底部间距 */
  }

  .card-description {
    font-size: clamp(0.9rem, 3vw, 1rem); /* 移动端描述大小 */
    margin-bottom: 12px; /* 减少描述底部间距 */
    line-height: 1.4; /* 优化行高 */
  }

  .card-features {
    display: flex; /* 移动端改为横向布局 */
    flex-direction: row; /* 水平排列 */
    flex-wrap: nowrap; /* 不换行 */
    gap: 8px; /* 横向间距，与桌面端一致 */
    margin-left: -20px; /* 负左边距突破card-content-section的左padding */
    margin-right: -20px; /* 负右边距突破card-content-section的右padding */
    margin-top: 0;
    margin-bottom: 0;
    overflow-x: auto; /* 自动横向滚动 */
    overflow-y: hidden; /* 隐藏垂直滚动 */
    padding: 8px 20px; /* 左右内边距20px补偿负边距，上下内边距8px */
    width: calc(100% + 40px); /* 宽度 = 内容区域宽度300px + 左右padding 40px = 340px */
    box-sizing: border-box; /* 确保padding包含在宽度内 */
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
    scrollbar-width: none; /* 隐藏滚动条 */
    -ms-overflow-style: none; /* IE隐藏滚动条 */
    scroll-behavior: smooth; /* 平滑滚动 */
  }

  /* 隐藏移动端滚动条 */
  .card-features::-webkit-scrollbar {
    display: none;
  }

  .card-features li {
    font-size: clamp(0.75rem, 1vw, 0.85rem); /* 与桌面端一致的字体大小 */
    color: #1d1d1f; /* 与桌面端一致的文字颜色 */
    position: relative;
    padding: 5px 10px 5px 18px; /* 与桌面端一致的内边距 */
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Segoe UI', Roboto, sans-serif; /* 与桌面端一致的字体 */
    line-height: 1.3;
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.12) 0%, rgba(29, 233, 182, 0.1) 100%); /* 与桌面端完全一致的背景 */
    border: 1px solid rgba(66, 165, 245, 0.2); /* 与桌面端一致的边框 */
    border-radius: 14px; /* 与桌面端一致的圆角 */
    white-space: nowrap;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* 与桌面端一致的过渡效果 */
    flex-shrink: 0; /* 防止收缩 */
    box-shadow: 0 2px 8px rgba(66, 165, 245, 0.08); /* 与桌面端一致的阴影 */
    font-weight: 500; /* 与桌面端一致的字重 */
    max-width: none; /* 确保标签不会被截断 */
  }

  /* 移动端悬停效果，与桌面端一致 */
  .card-features li:hover {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.18) 0%, rgba(29, 233, 182, 0.15) 100%);
    transform: translateY(-2px) scale(1.02); /* 更明显的悬停效果 */
    box-shadow: 0 4px 16px rgba(66, 165, 245, 0.15); /* 悬停时增强阴影 */
    border-color: rgba(66, 165, 245, 0.3);
  }



  /* 移动端 Progress Indicators 居中 */
  .progress-indicators {
    left: 50% !important;
    transform: translateX(-50%) !important;
    justify-content: center !important;
    margin-bottom: 20px !important; /* 增加底部间距 */
  }
}

  /* Mobile Card Positioning */
  .feature-card[data-card="1"] {
    transform: translateX(280px) scale(0.8);
  }

  .feature-card[data-card="2"] {
    transform: translateX(-280px) scale(0.8);
  }

  .feature-card[data-card="3"] {
    transform: translateX(560px) scale(0.6);
  }

  .feature-card[data-card="4"] {
    transform: translateX(-560px) scale(0.6);
  }

  /* Mobile Card Switching Animations */
  .cards-container.show-card-1 .feature-card[data-card="0"] {
    transform: translateX(-280px) scale(0.8);
  }

  .cards-container.show-card-1 .feature-card[data-card="2"] {
    transform: translateX(280px) scale(0.8);
  }

  .cards-container.show-card-1 .feature-card[data-card="3"] {
    transform: translateX(-560px) scale(0.6);
  }

  .cards-container.show-card-1 .feature-card[data-card="4"] {
    transform: translateX(560px) scale(0.6);
  }

  .cards-container.show-card-2 .feature-card[data-card="0"] {
    transform: translateX(560px) scale(0.6);
  }

  .cards-container.show-card-2 .feature-card[data-card="1"] {
    transform: translateX(-280px) scale(0.8);
  }

  .cards-container.show-card-2 .feature-card[data-card="3"] {
    transform: translateX(280px) scale(0.8);
  }

  .cards-container.show-card-2 .feature-card[data-card="4"] {
    transform: translateX(-560px) scale(0.6);
  }

  .cards-container.show-card-3 .feature-card[data-card="0"] {
    transform: translateX(-560px) scale(0.6);
  }

  .cards-container.show-card-3 .feature-card[data-card="1"] {
    transform: translateX(560px) scale(0.6);
  }

  .cards-container.show-card-3 .feature-card[data-card="2"] {
    transform: translateX(-280px) scale(0.8);
  }

  .cards-container.show-card-3 .feature-card[data-card="4"] {
    transform: translateX(280px) scale(0.8);
  }

  .cards-container.show-card-4 .feature-card[data-card="0"] {
    transform: translateX(280px) scale(0.8);
  }

  .cards-container.show-card-4 .feature-card[data-card="1"] {
    transform: translateX(560px) scale(0.6);
  }

  .cards-container.show-card-4 .feature-card[data-card="2"] {
    transform: translateX(-560px) scale(0.6);
  }

  .cards-container.show-card-4 .feature-card[data-card="3"] {
    transform: translateX(-280px) scale(0.8);
  }

  .card-content {
    padding: 30px;
  }

  .navigation-controls {
    gap: 30px;
    margin-top: 40px;
  }

  .nav-arrow {
    width: 45px;
    height: 45px;
  }

  .nav-arrow svg {
    width: 20px;
    height: 20px;
  }

  .scroll-lock-indicator {
    top: 10px;
    right: 10px;
    padding: 10px 16px;
    font-size: 12px;
  }
