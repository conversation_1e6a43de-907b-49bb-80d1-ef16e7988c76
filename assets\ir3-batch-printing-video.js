document.addEventListener('DOMContentLoaded', function() {
  const batchSection = document.querySelector('.ir3-batch-printing-video');
  const video = batchSection.querySelector('.batch-video');
  const featureItems = batchSection.querySelectorAll('.feature-item');

  // Video interaction
  if (video) {
    // Removed click to pause/play functionality - video will auto-play and loop

    // Add loading state
    video.addEventListener('loadstart', function() {
      video.style.opacity = '0.7';
    });

    video.addEventListener('canplay', function() {
      video.style.opacity = '1';
    });
  }

  // Feature items interaction
  featureItems.forEach((item, index) => {
    item.addEventListener('mouseenter', function() {
      // Add ripple effect
      const ripple = document.createElement('div');
      ripple.style.cssText = `
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle, rgba(66, 165, 245, 0.1) 0%, transparent 70%);
        border-radius: 12px;
        z-index: -1;
        animation: ripple 0.6s ease-out;
      `;

      this.style.position = 'relative';
      this.appendChild(ripple);

      setTimeout(() => {
        if (ripple.parentNode) {
          ripple.parentNode.removeChild(ripple);
        }
      }, 600);
    });
  });

  // Add CSS animation for ripple
  const style = document.createElement('style');
  style.textContent = `
    @keyframes ripple {
      0% {
        transform: scale(0);
        opacity: 1;
      }
      100% {
        transform: scale(1);
        opacity: 0;
      }
    }
  `;
  document.head.appendChild(style);
});
