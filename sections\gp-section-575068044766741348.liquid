

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-575068044766741348.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-575068044766741348.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-575068044766741348.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-575068044766741348.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-575068044766741348.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-575068044766741348.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-575068044766741348.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-575068044766741348.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-575068044766741348.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-575068044766741348.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-575068044766741348.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-575068044766741348.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-575068044766741348.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-575068044766741348.gps.gpsil [style*="--bbw:"]{border-bottom-width:var(--bbw)}.gps-575068044766741348.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-575068044766741348.gps.gpsil [style*="--hvr-bc:"]:hover{border-color:var(--hvr-bc)}.gps-575068044766741348.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-575068044766741348.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-575068044766741348.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-575068044766741348.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-575068044766741348.gps.gpsil [style*="--hvr-bs:"]:hover{border-style:var(--hvr-bs)}.gps-575068044766741348.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-575068044766741348.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-575068044766741348.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-575068044766741348.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-575068044766741348.gps.gpsil [style*="--hvr-bw:"]:hover{border-width:var(--hvr-bw)}.gps-575068044766741348.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-575068044766741348.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-575068044766741348.gps.gpsil [style*="--hvr-c:"]:hover{color:var(--hvr-c)}.gps-575068044766741348.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-575068044766741348.gps.gpsil [style*="--fd:"]{flex-direction:var(--fd)}.gps-575068044766741348.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-575068044766741348.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-575068044766741348.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-575068044766741348.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-575068044766741348.gps.gpsil [style*="--gg:"]{grid-gap:var(--gg)}.gps-575068044766741348.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-575068044766741348.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-575068044766741348.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-575068044766741348.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-575068044766741348.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-575068044766741348.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-575068044766741348.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-575068044766741348.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-575068044766741348.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-575068044766741348.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-575068044766741348.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-575068044766741348.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-575068044766741348.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-575068044766741348.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-575068044766741348.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-575068044766741348.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-575068044766741348.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-575068044766741348.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-575068044766741348.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-575068044766741348.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-575068044766741348.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-575068044766741348.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-575068044766741348.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-575068044766741348.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-575068044766741348.gps.gpsil [style*="--bbw-tablet:"]{border-bottom-width:var(--bbw-tablet)}.gps-575068044766741348.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-575068044766741348.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-575068044766741348.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-575068044766741348.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-575068044766741348.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-575068044766741348.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-575068044766741348.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-575068044766741348.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-575068044766741348.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-575068044766741348.gps.gpsil [style*="--bbw-mobile:"]{border-bottom-width:var(--bbw-mobile)}.gps-575068044766741348.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-575068044766741348.gps.gpsil [style*="--gg-mobile:"]{grid-gap:var(--gg-mobile)}.gps-575068044766741348.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-575068044766741348.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-575068044766741348.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-575068044766741348.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-575068044766741348.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-575068044766741348.gps.gpsil [style*="--pc-mobile:"]{place-content:var(--pc-mobile)}.gps-575068044766741348.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-575068044766741348.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-575068044766741348.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-575068044766741348.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-575068044766741348.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-575068044766741348.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-575068044766741348 .-gp-rotate-90,.gps-575068044766741348 .gp-rotate-180,.gps-575068044766741348 .gp-rotate-90{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-575068044766741348 .focus\:\!gp-shadow-none,.gps-575068044766741348 .hover\:\!gp-shadow-none{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-575068044766741348 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-575068044766741348 .gp-fixed{position:fixed}.gps-575068044766741348 .gp-relative{position:relative}.gps-575068044766741348 .gp-top-\[27px\]{top:27px}.gps-575068044766741348 .gp-z-1{z-index:1}.gps-575068044766741348 .gp-z-100{z-index:100}.gps-575068044766741348 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-575068044766741348 .gp-mb-0{margin-bottom:0}.gps-575068044766741348 .gp-block{display:block}.gps-575068044766741348 .gp-flex{display:flex}.gps-575068044766741348 .gp-inline-flex{display:inline-flex}.gps-575068044766741348 .gp-grid{display:grid}.gps-575068044766741348 .gp-contents{display:contents}.gps-575068044766741348 .\!gp-hidden{display:none!important}.gps-575068044766741348 .gp-hidden{display:none}.gps-575068044766741348 .\!gp-h-auto{height:auto!important}.gps-575068044766741348 .gp-h-auto{height:auto}.gps-575068044766741348 .gp-h-full{height:100%}.gps-575068044766741348 .gp-min-h-0{min-height:0}.gps-575068044766741348 .gp-w-full{width:100%}.gps-575068044766741348 .gp-max-w-full{max-width:100%}.gps-575068044766741348 .gp-flex-none{flex:none}.gps-575068044766741348 .-gp-rotate-90{--tw-rotate:-90deg}.gps-575068044766741348 .-gp-rotate-90,.gps-575068044766741348 .gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-575068044766741348 .gp-rotate-180{--tw-rotate:180deg}.gps-575068044766741348 .gp-rotate-90{--tw-rotate:90deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-575068044766741348 .gp-cursor-pointer{cursor:pointer}.gps-575068044766741348 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-575068044766741348 .gp-flex-col{flex-direction:column}.gps-575068044766741348 .gp-items-center{align-items:center}.gps-575068044766741348 .gp-justify-start{justify-content:flex-start}.gps-575068044766741348 .gp-justify-center{justify-content:center}.gps-575068044766741348 .gp-gap-y-0{row-gap:0}.gps-575068044766741348 .gp-overflow-hidden{overflow:hidden}.gps-575068044766741348 .gp-overflow-clip{overflow:clip}.gps-575068044766741348 .gp-break-words{overflow-wrap:break-word}.gps-575068044766741348 .gp-rounded{border-radius:4px}.gps-575068044766741348 .gp-rounded-none{border-radius:0}.gps-575068044766741348 .\!gp-bg-none{background-image:none!important}.gps-575068044766741348 .gp-p-\[16px\]{padding:16px}.gps-575068044766741348 .gp-text-center{text-align:center}.gps-575068044766741348 .gp-text-\[14px\]{font-size:14px}.gps-575068044766741348 .gp-leading-normal{line-height:1.5}.gps-575068044766741348 .gp-text-g-bg-3{color:var(--g-c-bg-3)}.gps-575068044766741348 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-575068044766741348 .gp-no-underline{text-decoration-line:none}.gps-575068044766741348 .\!gp-outline-none{outline:2px solid transparent!important;outline-offset:2px!important}.gps-575068044766741348 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-575068044766741348 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-575068044766741348 .gp-duration-200{transition-duration:.2s}.gps-575068044766741348 .gp-duration-300{transition-duration:.3s}.gps-575068044766741348 .gp-duration-500{transition-duration:.5s}.gps-575068044766741348 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-575068044766741348 .disabled\:gp-btn-disabled:disabled{cursor:default}@media (hover:hover) and (pointer:fine){.gps-575068044766741348 .hover\:gp-text-g-text-3:hover{color:var(--g-c-text-3)}.gps-575068044766741348 .hover\:\!gp-shadow-none:hover{--tw-shadow:0 0 #0000!important;--tw-shadow-colored:0 0 #0000!important;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)!important}}.gps-575068044766741348 .focus\:\!gp-shadow-none:focus{--tw-shadow:0 0 #0000!important;--tw-shadow-colored:0 0 #0000!important;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)!important}.gps-575068044766741348 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-575068044766741348 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-575068044766741348 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-575068044766741348 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-575068044766741348 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-575068044766741348 .tablet\:gp-block{display:block}.gps-575068044766741348 .tablet\:\!gp-hidden{display:none!important}.gps-575068044766741348 .tablet\:gp-hidden{display:none}.gps-575068044766741348 .tablet\:gp-h-auto{height:auto}.gps-575068044766741348 .tablet\:gp-flex-none{flex:none}}@media (max-width:767px){.gps-575068044766741348 .mobile\:gp-block{display:block}.gps-575068044766741348 .mobile\:\!gp-hidden{display:none!important}.gps-575068044766741348 .mobile\:gp-hidden{display:none}.gps-575068044766741348 .mobile\:gp-h-auto{height:auto}.gps-575068044766741348 .mobile\:gp-flex-none{flex:none}}.gps-575068044766741348 .\[\&\>svg\]\:\!gp-h-\[var\(--height-iconCollapseSize\)\]>svg{height:var(--height-iconCollapseSize)!important}.gps-575068044766741348 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}.gps-575068044766741348 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-575068044766741348 .\[\&_p\]\:gp-inline p{display:inline}.gps-575068044766741348 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-575068044766741348 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-575068044766741348 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gk3O8tfVqk" data-id="gk3O8tfVqk"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-4xl);--pt-mobile:var(--g-s-4xl);--pl-mobile:0px;--pr-mobile:0px;--pt-tablet:var(--g-s-4xl);--pl-tablet:0px;--pr-tablet:0px;--cg:0px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#242424;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gk3O8tfVqk gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gKzPTt2Fk1 gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gQ6M51H1dI" data-id="gQ6M51H1dI"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:0px;--pt:0px;--pl:15px;--pb:0px;--pr:15px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gQ6M51H1dI gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gthHbhc0so gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g2k34a46Zr">
    <div
      parentTag="Col"
        class="g2k34a46Zr "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(undefined)"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-bg-3 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--c:#FFFFFF;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:33px;--size-tablet:33px;--size-mobile:29px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg2k34a46Zr_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gFKkuZjXht">
    <div
      parentTag="Col"
        class="gFKkuZjXht "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--pl-mobile:15px;--pr-mobile:15px"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-3 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#FFFFFF;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggFKkuZjXht_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pt:var(--g-s-2xl);--pl:0px;--pb:24px;--pr:0px;--pt-mobile:var(--g-s-2xl);--pl-mobile:0px;--pb-mobile:24px;--pr-mobile:0px" class="g_Z4yHp451 ">
      
    <gp-form
      id="g_Z4yHp451"
      data-id="g_Z4yHp451"
      
      data-submit-action=''
      data-callback='{"link":"","target":"_self"}'
    >
      <a
        hidden
        id="gp-form-callback-g_Z4yHp451"
        href="" target=""
      >
      </a>
      {% form 'customer', class: 'gp-form-g_Z4yHp451 ', id: 'contact_form_g_Z4yHp451' %}
        {% if form.errors %}
          <div
            id="gp-form-error-g_Z4yHp451"
            class="gp-form-message gp-hidden gp-top-[27px] gp-z-100 gp-fixed gp-rounded gp-p-[16px] gp-text-[14px] gp-leading-normal"
            style="background-color:#FFE9E9;left:50%;transform:translateX(-50%);color:#EA3335"
          >
            {{ section.settings.gg_Z4yHp451_errorMessage }}
          </div>
        {% endif %}
        <div popover id="my-popover-g_Z4yHp451">
        <style>
            #my-popover-g_Z4yHp451::backdrop {
              width: fit-content;
              height: fit-content;
            }
        </style>
        <div
          id="gp-form-success-g_Z4yHp451"
          class="gp-form-message gp-hidden gp-top-[27px] gp-z-100 gp-fixed gp-rounded gp-p-[16px] gp-text-[14px] gp-leading-normal"
          style="background-color:#F2FFEC;left:50%;transform:translateX(-50%);color:#52C41A"
        >
          {{ section.settings.gg_Z4yHp451_successMessage }}
        </div></div>
        
       
      
    <div
      parentTag="Newsletter" id="gqh6IAiMM1" data-id="gqh6IAiMM1"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:0px;--cg:0px;--pc:start;--gtc:minmax(0, 9fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 8fr) minmax(0, 4fr);--w:570px;--w-tablet:570px;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gqh6IAiMM1 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gfaGaceJ5z gp-relative gp-flex gp-flex-col"
    >
      
    <div
    data-id="gjzTtGXohs"
      class="gjzTtGXohs"
      style="--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    >
      <input
        type="email"
        class="gp-form-item gp-g-paragraph-1 !gp-outline-none !gp-h-auto focus:!gp-shadow-none hover:!gp-shadow-none !gp-bg-none"
        style="--pl:16px;--pr:16px;--pt:12px;--pb:12px;--w:100%;--w-tablet:313px;--w-mobile:100%;--bs:none;--bw:0px 0px 0px 0px;--bc:transparent;--shadow:none"
        placeholder="{{ section.settings.ggjzTtGXohs_placeholder }}"
        {% if true %}
        required
        {% endif %}
        name="contact[email]"
        value=""
        autocomplete="email"
      ></input>
    </div>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gYIe0C5MY3 gp-relative gp-flex gp-flex-col"
    >
      
    
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
    
  >
    <style>
    .gpR8io_Y_h.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px 0px 0px 0px;
  border-color: transparent;
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .gpR8io_Y_h:hover::before {
      border-style: none;
  border-width: 0px 0px 0px 0px;
  border-color: transparent;
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .gpR8io_Y_h:hover .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

     .gpR8io_Y_h .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gpR8io_Y_h:hover .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gpR8io_Y_h .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gpR8io_Y_h .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gpR8io_Y_h:hover .gp-product-dot-price {
      color: var(--g-c-text-3, text-3);
    }
  </style>
    <button
      type="submit" data-id="gpR8io_Y_h" aria-label="Subscribe"
      
      data-state="idle"
      class="gpR8io_Y_h gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none hover:gp-text-g-text-3 gp-text-g-text-3 gp-g-paragraph-1"
      style="--hvr-bg:rgba(36, 36, 36, 0.8);--bg:#2352E7;--pl:24px;--pr:24px;--pt:12px;--pb:12px;--pl-tablet:24px;--pr-tablet:24px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:12px;--pb-mobile:12px;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--hvr-c:var(--g-c-text-3, text-3);--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--hvr-c:var(--g-c-text-3, text-3);--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggpR8io_Y_h_label }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

  
    </div>
    </div>
   
    
      {% endform %}
    </gp-form>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-form.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="g2d8J7jb8u" data-id="g2d8J7jb8u"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:none;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--pt:var(--g-s-xl);--pl:15px;--pb:30px;--pr:15px;--pt-mobile:30px;--pl-mobile:var(--g-s-l);--pb-mobile:30px;--pr-mobile:var(--g-s-l);--cg:29px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 6fr) minmax(0, 6fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g2d8J7jb8u gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="grze5u5kEi gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gk7beffKQi">
    <div
      parentTag="Col"
        class="gk7beffKQi "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(undefined)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-bg-3 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#FFFFFF;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggk7beffKQi_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-xl);--ta:left"
    
  >
    <style>
    .gawWvqnEpk.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gawWvqnEpk:hover::before {
      
      
    }

    .gawWvqnEpk:hover .gp-button-icon {
      color: undefined;
    }

     .gawWvqnEpk .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gawWvqnEpk:hover .gp-button-price {
      color: undefined;
    }

    .gawWvqnEpk .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gawWvqnEpk .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gawWvqnEpk:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gawWvqnEpk" aria-label="<p>About Us</p>"
      
      data-state="idle"
      class="gawWvqnEpk gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggawWvqnEpk_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .g1phFQFEk5.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .g1phFQFEk5:hover::before {
      
      
    }

    .g1phFQFEk5:hover .gp-button-icon {
      color: undefined;
    }

     .g1phFQFEk5 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .g1phFQFEk5:hover .gp-button-price {
      color: undefined;
    }

    .g1phFQFEk5 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .g1phFQFEk5 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .g1phFQFEk5:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="g1phFQFEk5" aria-label="<p>FAQ</p>"
      
      data-state="idle"
      class="g1phFQFEk5 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.gg1phFQFEk5_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .g3ytSRWdXZ.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .g3ytSRWdXZ:hover::before {
      
      
    }

    .g3ytSRWdXZ:hover .gp-button-icon {
      color: undefined;
    }

     .g3ytSRWdXZ .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .g3ytSRWdXZ:hover .gp-button-price {
      color: undefined;
    }

    .g3ytSRWdXZ .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .g3ytSRWdXZ .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .g3ytSRWdXZ:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="g3ytSRWdXZ" aria-label="<p>Terms &amp; Conditions</p>"
      
      data-state="idle"
      class="g3ytSRWdXZ gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.gg3ytSRWdXZ_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gPUn2MEm9F.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gPUn2MEm9F:hover::before {
      
      
    }

    .gPUn2MEm9F:hover .gp-button-icon {
      color: undefined;
    }

     .gPUn2MEm9F .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gPUn2MEm9F:hover .gp-button-price {
      color: undefined;
    }

    .gPUn2MEm9F .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gPUn2MEm9F .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gPUn2MEm9F:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gPUn2MEm9F" aria-label="<p>Privacy Policy</p>"
      
      data-state="idle"
      class="gPUn2MEm9F gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggPUn2MEm9F_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gpJBNBxgr8 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gJpleEeL_p">
    <div
      parentTag="Col"
        class="gJpleEeL_p "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(undefined)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-bg-3 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#FFFFFF;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggJpleEeL_p_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-xl);--ta:left"
    
  >
    <style>
    .gc58Oe2csd.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gc58Oe2csd:hover::before {
      
      
    }

    .gc58Oe2csd:hover .gp-button-icon {
      color: undefined;
    }

     .gc58Oe2csd .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gc58Oe2csd:hover .gp-button-price {
      color: undefined;
    }

    .gc58Oe2csd .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gc58Oe2csd .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gc58Oe2csd:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gc58Oe2csd" aria-label="<p>Home</p>"
      
      data-state="idle"
      class="gc58Oe2csd gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggc58Oe2csd_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gfkUSlLOWh.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gfkUSlLOWh:hover::before {
      
      
    }

    .gfkUSlLOWh:hover .gp-button-icon {
      color: undefined;
    }

     .gfkUSlLOWh .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gfkUSlLOWh:hover .gp-button-price {
      color: undefined;
    }

    .gfkUSlLOWh .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gfkUSlLOWh .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gfkUSlLOWh:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gfkUSlLOWh" aria-label="<p><u>3D Printing</u></p>"
      
      data-state="idle"
      class="gfkUSlLOWh gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggfkUSlLOWh_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .ghSG5YX2hP.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .ghSG5YX2hP:hover::before {
      
      
    }

    .ghSG5YX2hP:hover .gp-button-icon {
      color: undefined;
    }

     .ghSG5YX2hP .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .ghSG5YX2hP:hover .gp-button-price {
      color: undefined;
    }

    .ghSG5YX2hP .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .ghSG5YX2hP .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .ghSG5YX2hP:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="ghSG5YX2hP" aria-label="<p><u>Accessories</u></p>"
      
      data-state="idle"
      class="ghSG5YX2hP gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.gghSG5YX2hP_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gKmcnUOmrB.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gKmcnUOmrB:hover::before {
      
      
    }

    .gKmcnUOmrB:hover .gp-button-icon {
      color: undefined;
    }

     .gKmcnUOmrB .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gKmcnUOmrB:hover .gp-button-price {
      color: undefined;
    }

    .gKmcnUOmrB .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gKmcnUOmrB .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gKmcnUOmrB:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gKmcnUOmrB" aria-label="<p><u>Support</u></p>"
      
      data-state="idle"
      class="gKmcnUOmrB gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggKmcnUOmrB_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gs-anS4Vq0 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gnmX0s6Mf6">
    <div
      parentTag="Col"
        class="gnmX0s6Mf6 "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(undefined)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-bg-3 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#FFFFFF;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggnmX0s6Mf6_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-xl);--ta:left"
    
  >
    <style>
    .gKK83FsQyV.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gKK83FsQyV:hover::before {
      
      
    }

    .gKK83FsQyV:hover .gp-button-icon {
      color: undefined;
    }

     .gKK83FsQyV .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gKK83FsQyV:hover .gp-button-price {
      color: undefined;
    }

    .gKK83FsQyV .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gKK83FsQyV .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gKK83FsQyV:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gKK83FsQyV" aria-label="Customer Service"
      
      data-state="idle"
      class="gKK83FsQyV gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggKK83FsQyV_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gPZ2ugR_KZ.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gPZ2ugR_KZ:hover::before {
      
      
    }

    .gPZ2ugR_KZ:hover .gp-button-icon {
      color: undefined;
    }

     .gPZ2ugR_KZ .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gPZ2ugR_KZ:hover .gp-button-price {
      color: undefined;
    }

    .gPZ2ugR_KZ .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gPZ2ugR_KZ .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gPZ2ugR_KZ:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gPZ2ugR_KZ" aria-label="Returns & Exchanges"
      
      data-state="idle"
      class="gPZ2ugR_KZ gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggPZ2ugR_KZ_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gWY10kH8Lz.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gWY10kH8Lz:hover::before {
      
      
    }

    .gWY10kH8Lz:hover .gp-button-icon {
      color: undefined;
    }

     .gWY10kH8Lz .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gWY10kH8Lz:hover .gp-button-price {
      color: undefined;
    }

    .gWY10kH8Lz .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gWY10kH8Lz .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gWY10kH8Lz:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gWY10kH8Lz" aria-label="FAQs"
      
      data-state="idle"
      class="gWY10kH8Lz gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggWY10kH8Lz_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .grAH38Se9J.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .grAH38Se9J:hover::before {
      
      
    }

    .grAH38Se9J:hover .gp-button-icon {
      color: undefined;
    }

     .grAH38Se9J .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .grAH38Se9J:hover .gp-button-price {
      color: undefined;
    }

    .grAH38Se9J .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .grAH38Se9J .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .grAH38Se9J:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="grAH38Se9J" aria-label="Contact Us"
      
      data-state="idle"
      class="grAH38Se9J gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggrAH38Se9J_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gcpJHWaBTZ gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gs8gqO3Rog">
    <div
      parentTag="Col"
        class="gs8gqO3Rog "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(undefined)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-bg-3 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#FFFFFF;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggs8gqO3Rog_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gKQ-1YCWZA">
    <div
      parentTag="Col"
        class="gKQ-1YCWZA "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-xl)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-3 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#FFFFFF;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggKQ-1YCWZA_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gxB1Tycqfi">
    <div
      parentTag="Col"
        class="gxB1Tycqfi "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-3 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#FFFFFF;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggxB1Tycqfi_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
       
      
    <div
      parentTag="Col" id="grcChnbMfu" data-id="grcChnbMfu"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--cg:24px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="grcChnbMfu gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gf4N-Yu7J8 gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gCEzWn-XGJ"
    role="presentation"
    class="gp-group/image gCEzWn-XGJ gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
    >
      <a
        class="pointer-events-auto gp-h-full gp-flex"
        href="https://www.facebook.com/groups/911760202804295" target="_self" title="Image Title"
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/41bcb53a-329b-4d38-864a-290b550b82f5/-/format/auto/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTgiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTEwLTE4Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxMCIgaGVpZ2h0PSIxOCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTAiIGhlaWdodD0iMTgiIGZpbGw9InVybCgjZy0xMC0xOCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTEwIiB0bz0iMTAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/41bcb53a-329b-4d38-864a-290b550b82f5/-/format/auto/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTgiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTEwLTE4Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxMCIgaGVpZ2h0PSIxOCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTAiIGhlaWdodD0iMTgiIGZpbGw9InVybCgjZy0xMC0xOCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTEwIiB0bz0iMTAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTgiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTEwLTE4Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxMCIgaGVpZ2h0PSIxOCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTAiIGhlaWdodD0iMTgiIGZpbGw9InVybCgjZy0xMC0xOCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTEwIiB0bz0iMTAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="https://ucarecdn.com/41bcb53a-329b-4d38-864a-290b550b82f5/-/format/auto/"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:9px;--w-tablet:9px;--w-mobile:9px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </a>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gk4qd6AmYY gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="g0iHwAHXhe"
    role="presentation"
    class="gp-group/image g0iHwAHXhe gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:0px;--ta:left"
    >
      <a
        class="pointer-events-auto gp-h-full gp-flex"
        href="https://www.youtube.com/@zhuhaibeier" target="_blank" title="Image Title"
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/4191bd2e-dbe8-46a6-9f71-5fa0281afe39/-/format/auto/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTIwLTE1Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIxNSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIGZpbGw9InVybCgjZy0yMC0xNSkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTIwIiB0bz0iMjAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/4191bd2e-dbe8-46a6-9f71-5fa0281afe39/-/format/auto/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTIwLTE1Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIxNSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIGZpbGw9InVybCgjZy0yMC0xNSkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTIwIiB0bz0iMjAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTIwLTE1Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIxNSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIGZpbGw9InVybCgjZy0yMC0xNSkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTIwIiB0bz0iMjAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="https://ucarecdn.com/4191bd2e-dbe8-46a6-9f71-5fa0281afe39/-/format/auto/"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:20px;--w-tablet:20px;--w-mobile:20px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </a>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start;--d:none;--d-tablet:none;--d-mobile:none"
      class="gdKgw6CRya gp-relative gp-flex gp-flex-col"
    >
      
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start;--d:none;--d-tablet:none;--d-mobile:none"
      class="gpU6wHGPWk gp-relative gp-flex gp-flex-col"
    >
      
    </div>
    </div>
   
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gyh16zvcLj" data-id="gyh16zvcLj"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:none;--d-mobile:grid;--d-tablet:none;--op:100%;--radius:var(--g-radius-small);--pt:30px;--pb:30px;--pt-mobile:10px;--pl-mobile:15px;--pb-mobile:10px;--pr-mobile:15px;--cg:0px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gyh16zvcLj gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gegJyo0vIB gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)" class="gJ9SxFB6P8 ">
      
    <gp-accordion
      data-id="gJ9SxFB6P8"
     uid="gJ9SxFB6P8"
      class="gp-flex gp-w-full gp-flex-col "
      style="--gg:null;--gg-mobile:18px;border-radius:inherit"
      gp-data='{"setting":{"iconSvg":"<svg width=\"17\" height=\"16\" viewBox=\"0 0 17 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"M5.64645 2.64645C5.84171 2.45118 6.15829 2.45118 6.35355 2.64645L11.3536 7.64645C11.5488 7.84171 11.5488 8.15829 11.3536 8.35355L6.35355 13.3536C6.15829 13.5488 5.84171 13.5488 5.64645 13.3536C5.45118 13.1583 5.45118 12.8417 5.64645 12.6464L10.2929 8L5.64645 3.35355C5.45118 3.15829 5.45118 2.84171 5.64645 2.64645Z\" fill=\"currentColor\"/>\n                  </svg>","isIconPlus":false,"activeKey":1,"expanded":false,"expandItem":false,"iconPosition":"right","iconGlobalSize":{"desktop":{"gap":"16px","height":"16px","width":"16px"}},"layoutHeader":"text-only","expandedMode":"single","configIconSize":16,"parentUid":"gJ9SxFB6P8","childListNumber":[],"chidlrenUid":["gzM5Lo6VAv","gyU-12fwCq","gRypPDpnJd","gvJHJmluHv"]},"styles":{"color":{"active":"#FFFFFF","hover":"#FFFFFF","normal":"#FFFFFF"},"headerBorder":{"active":{"border":"solid","borderType":"style-1","borderWidth":"1px","color":"rgba(224, 224, 224, 0.1)","isCustom":true,"position":"bottom","width":"0px 0px 1px 0px"},"hover":{"border":"solid","borderType":"style-1","borderWidth":"1px","color":"rgba(224, 224, 224, 0.1)","isCustom":true,"position":"bottom","width":"0px 0px 1px 0px"},"normal":{"border":"solid","borderType":"style-1","borderWidth":"1px","color":"rgba(224, 224, 224, 0.1)","isCustom":true,"position":"bottom","width":"0px 0px 1px 0px"}},"fullWidth":{"desktop":true},"width":{"desktop":"1170px"},"itemHeaderSpacing":{"custom":{"mobile":{"horizontal":"0px","vertical":"10px"}}},"textColor":{"active":"#FFFFFF","hover":"#FFFFFF","normal":"#FFFFFF"},"iconColor":{"active":"#121212","hover":"#121212","normal":"#121212"},"contentSizePadding":{"desktop":{"gap":null,"padding":{"bottom":null,"left":null,"right":null,"top":null,"type":"custom"}},"mobile":{"gap":"18px","padding":{"bottom":"0px","left":"0px","right":"0px","top":"0px","type":"custom"}},"tablet":{"padding":{"bottom":null,"left":null,"right":null,"top":null,"type":"custom"}}},"headerContentPadding":{"desktop":{"padding":{"bottom":null,"left":null,"right":null,"top":null,"type":"custom"}},"mobile":{"padding":{"bottom":"10px","left":"0px","right":"0px","top":"10px","type":"custom"}},"tablet":{"padding":{"bottom":null,"left":null,"right":null,"top":null,"type":"custom"}}},"widthHeightSize":{"desktop":{"width":"100%"},"mobile":{"width":"100%"},"tablet":{"width":"100%"}}},"uid":"gJ9SxFB6P8"}'
    >
    <div class="gp-hidden gp-rotate-90 -gp-rotate-90 gp-rotate-180"></div>
    <div
                class="gp-flex"
                style="--jc:left"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:0px 0px 0px 0px;--bc:#121212">
    <div
      data-index="0"
      class="gJ9SxFB6P8 gp-accordion-item gp-overflow-hidden gp-child-item-gJ9SxFB6P8"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="gzM5Lo6VAv"
        id="gzM5Lo6VAv"
        data-index="0"
        class="gJ9SxFB6P8 gp-accordion-item-gJ9SxFB6P8-0 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:null;--pr:null;--pt:null;--pb:null;--pl-tablet:null;--pr-tablet:null;--pt-tablet:null;--pb-tablet:null;--pl-mobile:0px;--pr-mobile:0px;--pt-mobile:10px;--pb-mobile:10px;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-c:#FFFFFF;--c:#FFFFFF;--bs:solid;--hvr-bs:solid;--bw:0px 0px 1px 0px;--hvr-bw:0px 0px 1px 0px;--bc:rgba(224, 224, 224, 0.1);--hvr-bc:rgba(224, 224, 224, 0.1)"
      >
       <style class="accordion-style">.gp-accordion-item-gJ9SxFB6P8-0:hover 
      {
        .gp-collapsible-icon { 
          color: #FFFFFF !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#FFFFFF;--c:#FFFFFF;width:16px;height:16px"
            data-index="0"
            class="gJ9SxFB6P8 gp-accordion-item_icon gp-collapsible-icon gp-rotate-90 gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          ><svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5.64645 2.64645C5.84171 2.45118 6.15829 2.45118 6.35355 2.64645L11.3536 7.64645C11.5488 7.84171 11.5488 8.15829 11.3536 8.35355L6.35355 13.3536C6.15829 13.5488 5.84171 13.5488 5.64645 13.3536C5.45118 13.1583 5.45118 12.8417 5.64645 12.6464L10.2929 8L5.64645 3.35355C5.45118 3.15829 5.45118 2.84171 5.64645 2.64645Z" fill="currentColor"/>
                  </svg></span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggJ9SxFB6P8_childItem_0 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="gzM5Lo6VAv"
        data-index="0"
        data-show="false"
        class="gJ9SxFB6P8 gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="gzM5Lo6VAv"
        data-index="0"
        class="gJ9SxFB6P8 gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:null;--pr:null;--pl-tablet:null;--pr-tablet:null;--pl-mobile:0px;--pr-mobile:0px" >
            <div
      label="Block" tag="Col" type="component"
      
      class="gfu9x3PQGt gp-relative gp-flex gp-flex-col"
    >
      
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-xl);--ta:left"
    
  >
    <style>
    .gDOP9wrKPM.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gDOP9wrKPM:hover::before {
      
      
    }

    .gDOP9wrKPM:hover .gp-button-icon {
      color: undefined;
    }

     .gDOP9wrKPM .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gDOP9wrKPM:hover .gp-button-price {
      color: undefined;
    }

    .gDOP9wrKPM .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gDOP9wrKPM .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gDOP9wrKPM:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gDOP9wrKPM" aria-label="About"
      
      data-state="idle"
      class="gDOP9wrKPM gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggDOP9wrKPM_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gEf1ezkcYr.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gEf1ezkcYr:hover::before {
      
      
    }

    .gEf1ezkcYr:hover .gp-button-icon {
      color: undefined;
    }

     .gEf1ezkcYr .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gEf1ezkcYr:hover .gp-button-price {
      color: undefined;
    }

    .gEf1ezkcYr .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gEf1ezkcYr .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gEf1ezkcYr:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gEf1ezkcYr" aria-label="Events"
      
      data-state="idle"
      class="gEf1ezkcYr gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggEf1ezkcYr_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gmU75SBvav.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gmU75SBvav:hover::before {
      
      
    }

    .gmU75SBvav:hover .gp-button-icon {
      color: undefined;
    }

     .gmU75SBvav .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gmU75SBvav:hover .gp-button-price {
      color: undefined;
    }

    .gmU75SBvav .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gmU75SBvav .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gmU75SBvav:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gmU75SBvav" aria-label="Rentals"
      
      data-state="idle"
      class="gmU75SBvav gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggmU75SBvav_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gOkZNEfqrm.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gOkZNEfqrm:hover::before {
      
      
    }

    .gOkZNEfqrm:hover .gp-button-icon {
      color: undefined;
    }

     .gOkZNEfqrm .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gOkZNEfqrm:hover .gp-button-price {
      color: undefined;
    }

    .gOkZNEfqrm .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gOkZNEfqrm .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gOkZNEfqrm:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gOkZNEfqrm" aria-label="Features"
      
      data-state="idle"
      class="gOkZNEfqrm gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggOkZNEfqrm_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            <div
                class="gp-flex"
                style="--jc:left"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:0px 0px 0px 0px;--bc:#121212">
    <div
      data-index="1"
      class="gJ9SxFB6P8 gp-accordion-item gp-overflow-hidden gp-child-item-gJ9SxFB6P8"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="gyU-12fwCq"
        id="gyU-12fwCq"
        data-index="1"
        class="gJ9SxFB6P8 gp-accordion-item-gJ9SxFB6P8-1 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:null;--pr:null;--pt:null;--pb:null;--pl-tablet:null;--pr-tablet:null;--pt-tablet:null;--pb-tablet:null;--pl-mobile:0px;--pr-mobile:0px;--pt-mobile:10px;--pb-mobile:10px;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-c:#FFFFFF;--c:#FFFFFF;--bs:solid;--hvr-bs:solid;--bw:0px 0px 1px 0px;--hvr-bw:0px 0px 1px 0px;--bc:rgba(224, 224, 224, 0.1);--hvr-bc:rgba(224, 224, 224, 0.1)"
      >
       <style class="accordion-style">.gp-accordion-item-gJ9SxFB6P8-1:hover 
      {
        .gp-collapsible-icon { 
          color: #FFFFFF !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#FFFFFF;--c:#FFFFFF;width:16px;height:16px"
            data-index="1"
            class="gJ9SxFB6P8 gp-accordion-item_icon gp-collapsible-icon gp-rotate-90 gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          ><svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5.64645 2.64645C5.84171 2.45118 6.15829 2.45118 6.35355 2.64645L11.3536 7.64645C11.5488 7.84171 11.5488 8.15829 11.3536 8.35355L6.35355 13.3536C6.15829 13.5488 5.84171 13.5488 5.64645 13.3536C5.45118 13.1583 5.45118 12.8417 5.64645 12.6464L10.2929 8L5.64645 3.35355C5.45118 3.15829 5.45118 2.84171 5.64645 2.64645Z" fill="currentColor"/>
                  </svg></span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggJ9SxFB6P8_childItem_1 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="gyU-12fwCq"
        data-index="1"
        data-show="false"
        class="gJ9SxFB6P8 gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="gyU-12fwCq"
        data-index="1"
        class="gJ9SxFB6P8 gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:null;--pr:null;--pl-tablet:null;--pr-tablet:null;--pl-mobile:0px;--pr-mobile:0px" >
            <div
      label="Block" tag="Col" type="component"
      
      class="gq_NhpBs9V gp-relative gp-flex gp-flex-col"
    >
      
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-xl);--ta:left"
    
  >
    <style>
    .gblQMaQYu6.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gblQMaQYu6:hover::before {
      
      
    }

    .gblQMaQYu6:hover .gp-button-icon {
      color: undefined;
    }

     .gblQMaQYu6 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gblQMaQYu6:hover .gp-button-price {
      color: undefined;
    }

    .gblQMaQYu6 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gblQMaQYu6 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gblQMaQYu6:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gblQMaQYu6" aria-label="Men"
      
      data-state="idle"
      class="gblQMaQYu6 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggblQMaQYu6_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gqL931gHly.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gqL931gHly:hover::before {
      
      
    }

    .gqL931gHly:hover .gp-button-icon {
      color: undefined;
    }

     .gqL931gHly .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gqL931gHly:hover .gp-button-price {
      color: undefined;
    }

    .gqL931gHly .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gqL931gHly .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gqL931gHly:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gqL931gHly" aria-label="Women"
      
      data-state="idle"
      class="gqL931gHly gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggqL931gHly_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .grgQU1WygZ.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .grgQU1WygZ:hover::before {
      
      
    }

    .grgQU1WygZ:hover .gp-button-icon {
      color: undefined;
    }

     .grgQU1WygZ .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .grgQU1WygZ:hover .gp-button-price {
      color: undefined;
    }

    .grgQU1WygZ .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .grgQU1WygZ .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .grgQU1WygZ:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="grgQU1WygZ" aria-label="Footweat"
      
      data-state="idle"
      class="grgQU1WygZ gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggrgQU1WygZ_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gALaVi_0Df.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gALaVi_0Df:hover::before {
      
      
    }

    .gALaVi_0Df:hover .gp-button-icon {
      color: undefined;
    }

     .gALaVi_0Df .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gALaVi_0Df:hover .gp-button-price {
      color: undefined;
    }

    .gALaVi_0Df .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gALaVi_0Df .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gALaVi_0Df:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gALaVi_0Df" aria-label="Brands"
      
      data-state="idle"
      class="gALaVi_0Df gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggALaVi_0Df_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            <div
                class="gp-flex"
                style="--jc:left"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:0px 0px 0px 0px;--bc:#121212">
    <div
      data-index="2"
      class="gJ9SxFB6P8 gp-accordion-item gp-overflow-hidden gp-child-item-gJ9SxFB6P8"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="gRypPDpnJd"
        id="gRypPDpnJd"
        data-index="2"
        class="gJ9SxFB6P8 gp-accordion-item-gJ9SxFB6P8-2 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:null;--pr:null;--pt:null;--pb:null;--pl-tablet:null;--pr-tablet:null;--pt-tablet:null;--pb-tablet:null;--pl-mobile:0px;--pr-mobile:0px;--pt-mobile:10px;--pb-mobile:10px;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-c:#FFFFFF;--c:#FFFFFF;--bs:solid;--hvr-bs:solid;--bw:0px 0px 1px 0px;--hvr-bw:0px 0px 1px 0px;--bc:rgba(224, 224, 224, 0.1);--hvr-bc:rgba(224, 224, 224, 0.1)"
      >
       <style class="accordion-style">.gp-accordion-item-gJ9SxFB6P8-2:hover 
      {
        .gp-collapsible-icon { 
          color: #FFFFFF !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#FFFFFF;--c:#FFFFFF;width:16px;height:16px"
            data-index="2"
            class="gJ9SxFB6P8 gp-accordion-item_icon gp-collapsible-icon gp-rotate-90 gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          ><svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5.64645 2.64645C5.84171 2.45118 6.15829 2.45118 6.35355 2.64645L11.3536 7.64645C11.5488 7.84171 11.5488 8.15829 11.3536 8.35355L6.35355 13.3536C6.15829 13.5488 5.84171 13.5488 5.64645 13.3536C5.45118 13.1583 5.45118 12.8417 5.64645 12.6464L10.2929 8L5.64645 3.35355C5.45118 3.15829 5.45118 2.84171 5.64645 2.64645Z" fill="currentColor"/>
                  </svg></span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggJ9SxFB6P8_childItem_2 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="gRypPDpnJd"
        data-index="2"
        data-show="false"
        class="gJ9SxFB6P8 gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="gRypPDpnJd"
        data-index="2"
        class="gJ9SxFB6P8 gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:null;--pr:null;--pl-tablet:null;--pr-tablet:null;--pl-mobile:0px;--pr-mobile:0px" >
            <div
      label="Block" tag="Col" type="component"
      
      class="gU46UIlZcV gp-relative gp-flex gp-flex-col"
    >
      
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-xl);--ta:left"
    
  >
    <style>
    .gt_OoaJKt1.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gt_OoaJKt1:hover::before {
      
      
    }

    .gt_OoaJKt1:hover .gp-button-icon {
      color: undefined;
    }

     .gt_OoaJKt1 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gt_OoaJKt1:hover .gp-button-price {
      color: undefined;
    }

    .gt_OoaJKt1 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gt_OoaJKt1 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gt_OoaJKt1:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gt_OoaJKt1" aria-label="Customer Service"
      
      data-state="idle"
      class="gt_OoaJKt1 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggt_OoaJKt1_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gXXDN3mLcR.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gXXDN3mLcR:hover::before {
      
      
    }

    .gXXDN3mLcR:hover .gp-button-icon {
      color: undefined;
    }

     .gXXDN3mLcR .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gXXDN3mLcR:hover .gp-button-price {
      color: undefined;
    }

    .gXXDN3mLcR .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gXXDN3mLcR .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gXXDN3mLcR:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gXXDN3mLcR" aria-label="Returns & Exchanges"
      
      data-state="idle"
      class="gXXDN3mLcR gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggXXDN3mLcR_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gYSuyH-nRF.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gYSuyH-nRF:hover::before {
      
      
    }

    .gYSuyH-nRF:hover .gp-button-icon {
      color: undefined;
    }

     .gYSuyH-nRF .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gYSuyH-nRF:hover .gp-button-price {
      color: undefined;
    }

    .gYSuyH-nRF .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gYSuyH-nRF .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gYSuyH-nRF:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gYSuyH-nRF" aria-label="FAQs"
      
      data-state="idle"
      class="gYSuyH-nRF gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggYSuyH-nRF_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .g4JpubbYDz.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .g4JpubbYDz:hover::before {
      
      
    }

    .g4JpubbYDz:hover .gp-button-icon {
      color: undefined;
    }

     .g4JpubbYDz .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .g4JpubbYDz:hover .gp-button-price {
      color: undefined;
    }

    .g4JpubbYDz .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .g4JpubbYDz .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .g4JpubbYDz:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="g4JpubbYDz" aria-label="Contact Us"
      
      data-state="idle"
      class="g4JpubbYDz gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.gg4JpubbYDz_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            <div
                class="gp-flex"
                style="--jc:left"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:0px 0px 0px 0px;--bc:#121212">
    <div
      data-index="3"
      class="gJ9SxFB6P8 gp-accordion-item gp-overflow-hidden gp-child-item-gJ9SxFB6P8"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="gvJHJmluHv"
        id="gvJHJmluHv"
        data-index="3"
        class="gJ9SxFB6P8 gp-accordion-item-gJ9SxFB6P8-3 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:null;--pr:null;--pt:null;--pb:null;--pl-tablet:null;--pr-tablet:null;--pt-tablet:null;--pb-tablet:null;--pl-mobile:0px;--pr-mobile:0px;--pt-mobile:10px;--pb-mobile:10px;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-c:#FFFFFF;--c:#FFFFFF;--bs:solid;--hvr-bs:solid;--bw:0px 0px 1px 0px;--hvr-bw:0px 0px 1px 0px;--bc:rgba(224, 224, 224, 0.1);--hvr-bc:rgba(224, 224, 224, 0.1)"
      >
       <style class="accordion-style">.gp-accordion-item-gJ9SxFB6P8-3:hover 
      {
        .gp-collapsible-icon { 
          color: #FFFFFF !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#FFFFFF;--c:#FFFFFF;width:16px;height:16px"
            data-index="3"
            class="gJ9SxFB6P8 gp-accordion-item_icon gp-collapsible-icon gp-rotate-90 gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          ><svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5.64645 2.64645C5.84171 2.45118 6.15829 2.45118 6.35355 2.64645L11.3536 7.64645C11.5488 7.84171 11.5488 8.15829 11.3536 8.35355L6.35355 13.3536C6.15829 13.5488 5.84171 13.5488 5.64645 13.3536C5.45118 13.1583 5.45118 12.8417 5.64645 12.6464L10.2929 8L5.64645 3.35355C5.45118 3.15829 5.45118 2.84171 5.64645 2.64645Z" fill="currentColor"/>
                  </svg></span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggJ9SxFB6P8_childItem_3 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="gvJHJmluHv"
        data-index="3"
        data-show="false"
        class="gJ9SxFB6P8 gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="gvJHJmluHv"
        data-index="3"
        class="gJ9SxFB6P8 gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:null;--pr:null;--pl-tablet:null;--pr-tablet:null;--pl-mobile:0px;--pr-mobile:0px" >
            <div
      tag="Col" type="component"
      style="--ai:normal;--jc:normal;--o:0"
      class="gE5-cHTPZm gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gJJifK0Umg">
    <div
      parentTag="Col"
        class="gJJifK0Umg "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-xl)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-3 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:var(--g-c-text-3, text-3);word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggJJifK0Umg_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gNX6waModA">
    <div
      parentTag="Col"
        class="gNX6waModA "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--mt-mobile:var(--g-s-l);--mb-mobile:0px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-3 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:var(--g-c-text-3, text-3);word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggNX6waModA_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gH2bdbY5XB">
    <div
      parentTag="Col"
        class="gH2bdbY5XB "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-3 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:var(--g-c-text-3, text-3);word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggH2bdbY5XB_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
       
      
    <div
      parentTag="Col" id="g5iFg49H_U" data-id="g5iFg49H_U"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--cg:24px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g5iFg49H_U gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gPvGYu3E1D gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gBnA01meNW"
    role="presentation"
    class="gp-group/image gBnA01meNW gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/41bcb53a-329b-4d38-864a-290b550b82f5/-/format/auto/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTgiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTEwLTE4Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxMCIgaGVpZ2h0PSIxOCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTAiIGhlaWdodD0iMTgiIGZpbGw9InVybCgjZy0xMC0xOCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTEwIiB0bz0iMTAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/41bcb53a-329b-4d38-864a-290b550b82f5/-/format/auto/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTgiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTEwLTE4Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxMCIgaGVpZ2h0PSIxOCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTAiIGhlaWdodD0iMTgiIGZpbGw9InVybCgjZy0xMC0xOCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTEwIiB0bz0iMTAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTgiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTEwLTE4Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxMCIgaGVpZ2h0PSIxOCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTAiIGhlaWdodD0iMTgiIGZpbGw9InVybCgjZy0xMC0xOCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTEwIiB0bz0iMTAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="https://ucarecdn.com/41bcb53a-329b-4d38-864a-290b550b82f5/-/format/auto/"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:9px;--w-tablet:9px;--w-mobile:9px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g9lY3I8C8w gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gT6rb5e1Z6"
    role="presentation"
    class="gp-group/image gT6rb5e1Z6 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/d4711445-4efd-4fcc-be4b-dc5ab891386f/-/format/auto/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE4LTE0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOCIgaGVpZ2h0PSIxNCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTgiIGhlaWdodD0iMTQiIGZpbGw9InVybCgjZy0xOC0xNCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE4IiB0bz0iMTgiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/d4711445-4efd-4fcc-be4b-dc5ab891386f/-/format/auto/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE4LTE0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOCIgaGVpZ2h0PSIxNCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTgiIGhlaWdodD0iMTQiIGZpbGw9InVybCgjZy0xOC0xNCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE4IiB0bz0iMTgiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE4LTE0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOCIgaGVpZ2h0PSIxNCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTgiIGhlaWdodD0iMTQiIGZpbGw9InVybCgjZy0xOC0xNCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE4IiB0bz0iMTgiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="https://ucarecdn.com/d4711445-4efd-4fcc-be4b-dc5ab891386f/-/format/auto/"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:16px;--w-tablet:16px;--w-mobile:16px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gdCAhD6EJD gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="geYiPq3EJy"
    role="presentation"
    class="gp-group/image geYiPq3EJy gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/91a9e7a8-52a4-4698-b378-d1d7ce6d859d/-/format/auto/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE4LTE4Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIGZpbGw9InVybCgjZy0xOC0xOCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE4IiB0bz0iMTgiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/91a9e7a8-52a4-4698-b378-d1d7ce6d859d/-/format/auto/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE4LTE4Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIGZpbGw9InVybCgjZy0xOC0xOCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE4IiB0bz0iMTgiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE4LTE4Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIGZpbGw9InVybCgjZy0xOC0xOCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE4IiB0bz0iMTgiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="https://ucarecdn.com/91a9e7a8-52a4-4698-b378-d1d7ce6d859d/-/format/auto/"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:18px;--w-tablet:18px;--w-mobile:18px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gkhzKPHfbC gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="g2ph0JOJCy"
    role="presentation"
    class="gp-group/image g2ph0JOJCy gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/4191bd2e-dbe8-46a6-9f71-5fa0281afe39/-/format/auto/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTIwLTE1Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIxNSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIGZpbGw9InVybCgjZy0yMC0xNSkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTIwIiB0bz0iMjAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/4191bd2e-dbe8-46a6-9f71-5fa0281afe39/-/format/auto/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTIwLTE1Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIxNSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIGZpbGw9InVybCgjZy0yMC0xNSkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTIwIiB0bz0iMjAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTIwLTE1Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIxNSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIGZpbGw9InVybCgjZy0yMC0xNSkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTIwIiB0bz0iMjAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="https://ucarecdn.com/4191bd2e-dbe8-46a6-9f71-5fa0281afe39/-/format/auto/"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:20px;--w-tablet:20px;--w-mobile:20px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div>
    </div>
   
    
    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            
    </gp-accordion>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-accordion.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    </div>
    </div>
   
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--pt:16px;--pb:16px" class="gqmGAahmuL ">
      
    <div
    data-id="gqmGAahmuL"
      class="gp-flex gp-justify-start"
    >
      <div
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--t:gp-rotate(0deg);--bc:rgba(224, 224, 224, 0.1)"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:rgba(224, 224, 224, 0.1);--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:rgba(224, 224, 224, 0.1);--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--minw:100px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:rgba(224, 224, 224, 0.1);--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--minw:100px"
    ></div>
  
      </div>
    </div>
  
      </div>
       
      
    <div
      parentTag="Col" id="gyCxQXCac-" data-id="gyCxQXCac-"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--pt:30px;--pl:15px;--pb:30px;--pr:15px;--pt-mobile:var(--g-s-l);--pl-mobile:15px;--pb-mobile:30px;--pr-mobile:15px;--cg:30px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gyCxQXCac- gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="goTy_dkhYV gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="giKMddd6VY">
    <div
      parentTag="Col"
        class="giKMddd6VY "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-3 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:rgba(255, 255, 255, 0.8);--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggiKMddd6VY_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gf_iQJibHb gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="giYH7ECqUH" data-id="giYH7ECqUH"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mt-mobile:14px;--cg:8px;--pc:end;--pc-mobile:start;--gtc:minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="giYH7ECqUH gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:between;--o:0"
      class="gEbd_Goe1H gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="g4kRLaHXz0"
    role="presentation"
    class="gp-group/image g4kRLaHXz0 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/7c3de24c-f954-47b9-a7a4-0901c996a852/-/format/auto/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTQwLTI0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI0MCIgaGVpZ2h0PSIyNCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIGZpbGw9InVybCgjZy00MC0yNCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTQwIiB0bz0iNDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/7c3de24c-f954-47b9-a7a4-0901c996a852/-/format/auto/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTQwLTI0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI0MCIgaGVpZ2h0PSIyNCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIGZpbGw9InVybCgjZy00MC0yNCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTQwIiB0bz0iNDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTQwLTI0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI0MCIgaGVpZ2h0PSIyNCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIGZpbGw9InVybCgjZy00MC0yNCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTQwIiB0bz0iNDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="https://ucarecdn.com/7c3de24c-f954-47b9-a7a4-0901c996a852/-/format/auto/"
        width="100%"
        alt="Alt Image"
        style="--objf:cover;--w:38px;--w-tablet:38px;--w-mobile:38px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:between"
      class="g1ejpOu6TK gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gbXdeloRkW"
    role="presentation"
    class="gp-group/image gbXdeloRkW gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/65397dd7-5ca3-44ee-9b19-3ebd215dcf38/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDIiIGhlaWdodD0iMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTQyLTI0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI0MiIgaGVpZ2h0PSIyNCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDIiIGhlaWdodD0iMjQiIGZpbGw9InVybCgjZy00Mi0yNCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTQyIiB0bz0iNDIiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/65397dd7-5ca3-44ee-9b19-3ebd215dcf38/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDIiIGhlaWdodD0iMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTQyLTI0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI0MiIgaGVpZ2h0PSIyNCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDIiIGhlaWdodD0iMjQiIGZpbGw9InVybCgjZy00Mi0yNCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTQyIiB0bz0iNDIiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDIiIGhlaWdodD0iMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTQyLTI0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI0MiIgaGVpZ2h0PSIyNCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDIiIGhlaWdodD0iMjQiIGZpbGw9InVybCgjZy00Mi0yNCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTQyIiB0bz0iNDIiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="https://ucarecdn.com/65397dd7-5ca3-44ee-9b19-3ebd215dcf38/"
        width="100%"
        alt="Alt Image"
        style="--objf:cover;--w:38px;--w-tablet:38px;--w-mobile:38px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:between"
      class="ghd-QyzDUh gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="giyc27eYWS"
    role="presentation"
    class="gp-group/image giyc27eYWS gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/85fc0e7b-3a48-41b8-adc2-5e6983a7fb4e/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDIiIGhlaWdodD0iMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTQyLTI0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI0MiIgaGVpZ2h0PSIyNCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDIiIGhlaWdodD0iMjQiIGZpbGw9InVybCgjZy00Mi0yNCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTQyIiB0bz0iNDIiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/85fc0e7b-3a48-41b8-adc2-5e6983a7fb4e/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDIiIGhlaWdodD0iMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTQyLTI0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI0MiIgaGVpZ2h0PSIyNCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDIiIGhlaWdodD0iMjQiIGZpbGw9InVybCgjZy00Mi0yNCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTQyIiB0bz0iNDIiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDIiIGhlaWdodD0iMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTQyLTI0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI0MiIgaGVpZ2h0PSIyNCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDIiIGhlaWdodD0iMjQiIGZpbGw9InVybCgjZy00Mi0yNCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTQyIiB0bz0iNDIiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="https://ucarecdn.com/85fc0e7b-3a48-41b8-adc2-5e6983a7fb4e/"
        width="100%"
        alt="Alt Image"
        style="--objf:cover;--w:38px;--w-tablet:38px;--w-mobile:38px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:between"
      class="gv7ECfQCEI gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gv_wMmYjRj"
    role="presentation"
    class="gp-group/image gv_wMmYjRj gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/5bebff9f-2a1d-45fa-b559-bbcb57969fe4/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTQwLTI0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI0MCIgaGVpZ2h0PSIyNCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIGZpbGw9InVybCgjZy00MC0yNCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTQwIiB0bz0iNDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/5bebff9f-2a1d-45fa-b559-bbcb57969fe4/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTQwLTI0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI0MCIgaGVpZ2h0PSIyNCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIGZpbGw9InVybCgjZy00MC0yNCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTQwIiB0bz0iNDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTQwLTI0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI0MCIgaGVpZ2h0PSIyNCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIGZpbGw9InVybCgjZy00MC0yNCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTQwIiB0bz0iNDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="https://ucarecdn.com/5bebff9f-2a1d-45fa-b559-bbcb57969fe4/"
        width="100%"
        alt="Alt Image"
        style="--objf:cover;--w:38px;--w-tablet:38px;--w-mobile:38px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:between"
      class="gH8sl6xGP7 gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gJhuJ2Dxlj"
    role="presentation"
    class="gp-group/image gJhuJ2Dxlj gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/c5993898-7e4e-4974-a8a6-22c5213dded3/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTQwLTI0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI0MCIgaGVpZ2h0PSIyNCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIGZpbGw9InVybCgjZy00MC0yNCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTQwIiB0bz0iNDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/c5993898-7e4e-4974-a8a6-22c5213dded3/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTQwLTI0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI0MCIgaGVpZ2h0PSIyNCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIGZpbGw9InVybCgjZy00MC0yNCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTQwIiB0bz0iNDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTQwLTI0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI0MCIgaGVpZ2h0PSIyNCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIGZpbGw9InVybCgjZy00MC0yNCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTQwIiB0bz0iNDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="https://ucarecdn.com/c5993898-7e4e-4974-a8a6-22c5213dded3/"
        width="100%"
        alt="Alt Image"
        style="--objf:cover;--w:38px;--w-tablet:38px;--w-mobile:38px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 14",
    "tag": "section",
    "class": "gps-575068044766741348 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=575053851124565104&sectionId=575068044766741348)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"gg2k34a46Zr_text","label":"gg2k34a46Zr_text","default":"<p>Subscribe Today</p>"},{"type":"html","id":"ggFKkuZjXht_text","label":"ggFKkuZjXht_text","default":"<p>Sign up for exclusive content, special prizes, and latest update</p>"},{"type":"html","id":"gg_Z4yHp451_successMessage","label":"gg_Z4yHp451_successMessage","default":"Thanks for contacting us. We'll get back to you as soon as possible."},{"type":"html","id":"gg_Z4yHp451_errorMessage","label":"gg_Z4yHp451_errorMessage","default":"Can’t send email. Please try again later."},{"type":"html","id":"ggjzTtGXohs_placeholder","label":"ggjzTtGXohs_placeholder","default":"Enter your email"},{"type":"html","id":"ggpR8io_Y_h_label","label":"ggpR8io_Y_h_label","default":"Subscribe"},{"type":"html","id":"ggk7beffKQi_text","label":"ggk7beffKQi_text","default":"<p><strong>About Us&nbsp;</strong></p>"},{"type":"html","id":"ggawWvqnEpk_label","label":"ggawWvqnEpk_label","default":"<p>About Us</p>"},{"type":"html","id":"gg1phFQFEk5_label","label":"gg1phFQFEk5_label","default":"<p>FAQ</p>"},{"type":"html","id":"gg3ytSRWdXZ_label","label":"gg3ytSRWdXZ_label","default":"<p>Terms &amp; Conditions</p>"},{"type":"html","id":"ggPUn2MEm9F_label","label":"ggPUn2MEm9F_label","default":"<p>Privacy Policy</p>"},{"type":"html","id":"ggJpleEeL_p_text","label":"ggJpleEeL_p_text","default":"<p><strong>Categories</strong></p>"},{"type":"html","id":"ggc58Oe2csd_label","label":"ggc58Oe2csd_label","default":"<p>Home</p>"},{"type":"html","id":"ggfkUSlLOWh_label","label":"ggfkUSlLOWh_label","default":"<p><u>3D Printing</u></p>"},{"type":"html","id":"gghSG5YX2hP_label","label":"gghSG5YX2hP_label","default":"<p><u>Accessories</u></p>"},{"type":"html","id":"ggKmcnUOmrB_label","label":"ggKmcnUOmrB_label","default":"<p><u>Support</u></p>"},{"type":"html","id":"ggnmX0s6Mf6_text","label":"ggnmX0s6Mf6_text","default":"<p><strong>Help</strong></p>"},{"type":"html","id":"ggKK83FsQyV_label","label":"ggKK83FsQyV_label","default":"Customer Service"},{"type":"html","id":"ggPZ2ugR_KZ_label","label":"ggPZ2ugR_KZ_label","default":"Returns & Exchanges"},{"type":"html","id":"ggWY10kH8Lz_label","label":"ggWY10kH8Lz_label","default":"FAQs"},{"type":"html","id":"ggrAH38Se9J_label","label":"ggrAH38Se9J_label","default":"Contact Us"},{"type":"html","id":"ggs8gqO3Rog_text","label":"ggs8gqO3Rog_text","default":"<p><strong>Visit</strong></p>"},{"type":"html","id":"ggKQ-1YCWZA_text","label":"ggKQ-1YCWZA_text","default":"<p>4th Floor, No. 4 Plant, 6898 Zhuhai Avenue, Hongqi Town, Jinwan District, Zhuhai City</p>"},{"type":"html","id":"ggxB1Tycqfi_text","label":"ggxB1Tycqfi_text","default":"<p>&nbsp;<EMAIL></p>"},{"type":"html","id":"ggJ9SxFB6P8_childItem_0","label":"ggJ9SxFB6P8_childItem_0","default":"Company"},{"type":"html","id":"ggJ9SxFB6P8_childItem_1","label":"ggJ9SxFB6P8_childItem_1","default":"Shop"},{"type":"html","id":"ggJ9SxFB6P8_childItem_2","label":"ggJ9SxFB6P8_childItem_2","default":"Help"},{"type":"html","id":"ggJ9SxFB6P8_childItem_3","label":"ggJ9SxFB6P8_childItem_3","default":"Visit"},{"type":"html","id":"ggDOP9wrKPM_label","label":"ggDOP9wrKPM_label","default":"About"},{"type":"html","id":"ggEf1ezkcYr_label","label":"ggEf1ezkcYr_label","default":"Events"},{"type":"html","id":"ggmU75SBvav_label","label":"ggmU75SBvav_label","default":"Rentals"},{"type":"html","id":"ggOkZNEfqrm_label","label":"ggOkZNEfqrm_label","default":"Features"},{"type":"html","id":"ggblQMaQYu6_label","label":"ggblQMaQYu6_label","default":"Men"},{"type":"html","id":"ggqL931gHly_label","label":"ggqL931gHly_label","default":"Women"},{"type":"html","id":"ggrgQU1WygZ_label","label":"ggrgQU1WygZ_label","default":"Footweat"},{"type":"html","id":"ggALaVi_0Df_label","label":"ggALaVi_0Df_label","default":"Brands"},{"type":"html","id":"ggt_OoaJKt1_label","label":"ggt_OoaJKt1_label","default":"Customer Service"},{"type":"html","id":"ggXXDN3mLcR_label","label":"ggXXDN3mLcR_label","default":"Returns & Exchanges"},{"type":"html","id":"ggYSuyH-nRF_label","label":"ggYSuyH-nRF_label","default":"FAQs"},{"type":"html","id":"gg4JpubbYDz_label","label":"gg4JpubbYDz_label","default":"Contact Us"},{"type":"html","id":"ggJJifK0Umg_text","label":"ggJJifK0Umg_text","default":"<p>261 NW 26th Street Miami. FL 33127</p>"},{"type":"html","id":"ggNX6waModA_text","label":"ggNX6waModA_text","default":"<p>999-999-999</p>"},{"type":"html","id":"ggH2bdbY5XB_text","label":"ggH2bdbY5XB_text","default":"<p><EMAIL></p>"},{"type":"html","id":"ggiKMddd6VY_text","label":"ggiKMddd6VY_text","default":"<p>Copyright © 2022 GemThemes. All Rights Reserved.</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
