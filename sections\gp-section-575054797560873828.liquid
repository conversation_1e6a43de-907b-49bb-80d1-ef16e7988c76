

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-575054797560873828.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-575054797560873828.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-575054797560873828.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-575054797560873828.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-575054797560873828.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-575054797560873828.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-575054797560873828.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-575054797560873828.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-575054797560873828.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-575054797560873828.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-575054797560873828.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-575054797560873828.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-575054797560873828.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-575054797560873828.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-575054797560873828.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-575054797560873828.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-575054797560873828.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-575054797560873828.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-575054797560873828.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-575054797560873828.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-575054797560873828.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-575054797560873828.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-575054797560873828.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-575054797560873828.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-575054797560873828.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-575054797560873828.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-575054797560873828.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-575054797560873828.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-575054797560873828.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-575054797560873828.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-575054797560873828.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-575054797560873828.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-575054797560873828.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-575054797560873828.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-575054797560873828.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-575054797560873828.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-575054797560873828.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-575054797560873828 .gp-g-heading-1{font-family:var(--g-h1-ff);font-size:var(--g-h1-size);font-style:var(--g-h1-fs);font-weight:var(--g-h1-weight);letter-spacing:var(--g-h1-ls);line-height:var(--g-h1-lh)}.gps-575054797560873828 .gp-relative{position:relative}.gps-575054797560873828 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-575054797560873828 .gp-mb-0{margin-bottom:0}.gps-575054797560873828 .gp-flex{display:flex}.gps-575054797560873828 .gp-grid{display:grid}.gps-575054797560873828 .\!gp-hidden{display:none!important}.gps-575054797560873828 .gp-hidden{display:none}.gps-575054797560873828 .gp-max-w-full{max-width:100%}.gps-575054797560873828 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-575054797560873828 .gp-flex-col{flex-direction:column}.gps-575054797560873828 .gp-gap-y-0{row-gap:0}.gps-575054797560873828 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-575054797560873828 .gp-duration-200{transition-duration:.2s}.gps-575054797560873828 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-575054797560873828 .tablet\:\!gp-hidden{display:none!important}.gps-575054797560873828 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-575054797560873828 .mobile\:\!gp-hidden{display:none!important}.gps-575054797560873828 .mobile\:gp-hidden{display:none}}.gps-575054797560873828 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-575054797560873828 .\[\&_p\]\:gp-inline p{display:inline}.gps-575054797560873828 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-575054797560873828 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="g4IfFgiWWR" data-id="g4IfFgiWWR"
        style="--blockPadding:base;--pt:var(--g-s-2xl);--pb:var(--g-s-2xl);--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g4IfFgiWWR gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g0OAqTfNxD gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmkg0m-CgA">
    <div
      parentTag="Col"
        class="gmkg0m-CgA "
        style="--ta:center;--mt:0px;--pt:40px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-heading-1 gp-text"
          style="--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--ta:left;--weight:bold;--c:var(--g-c-text-2, text-2);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggmkg0m-CgA_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 6",
    "tag": "section",
    "class": "gps-575054797560873828 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=575053851124565104&sectionId=575054797560873828)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggmkg0m-CgA_text","label":"ggmkg0m-CgA_text","default":"What Top YouTubers Say About IR3 V2"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
