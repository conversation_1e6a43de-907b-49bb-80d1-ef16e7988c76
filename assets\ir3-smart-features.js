/**
 * IR3 Smart Features - Apple Style Horizontal Card Carousel
 * File: assets/ir3-smart-features.js
 * 16:9 aspect ratio cards with 3-card carousel layout, scroll lock, and GSAP animations
 */

document.addEventListener('DOMContentLoaded', function() {
  console.log('🚀 IR3 Smart Features initializing...');

  // Check GSAP and ScrollTrigger availability
  if (typeof gsap === 'undefined' || typeof ScrollTrigger === 'undefined') {
    console.error('❌ GSAP or ScrollTrigger not available');
    return;
  }

  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);
  console.log('✅ GSAP and ScrollTrigger available and registered');

  // Configuration
  const CONFIG = {
    TOTAL_CARDS: 5,
    SCROLL_LOCK_ENABLED: true,
    AUTO_PLAY_DELAY: 6000,
    ANIMATION_DURATION: 800,
    KEYBOARD_ENABLED: true,
    TOUCH_ENABLED: true,
    WHEEL_ENABLED: true,
    WHEEL_THRESHOLD: 50,
    DEBOUNCE_DELAY: 300
  };

  // State management
  let currentCardIndex = 0;
  let viewedCards = new Set([0]); // Track which cards have been viewed
  let isScrollLocked = false;
  let isInSection = false;
  let autoPlayTimer = null;
  let isAnimating = false;
  let wheelDelta = 0;
  let lastWheelTime = 0;
  let isDelayedUnlockInProgress = false; // Track if delayed unlock is in progress
  let delayedUnlockTimer = null; // Store the delayed unlock timer

  // Touch handling
  let touchStartX = 0;
  let touchStartY = 0;
  let touchEndX = 0;
  let touchEndY = 0;

  // Enhanced scroll velocity tracking (based on ir3-v2-key-features)
  let scrollVelocityHistory = [];
  let scrollStabilityTimer = null;
  let isScrollStable = true;
  let consecutiveScrollCount = 0;
  let lastScrollDirection = 0;
  let lastScrollTime = 0;
  let scrollCooldown = false;

  // Enhanced scroll velocity tracking constants (optimized for smart-features)
  const VELOCITY_HISTORY_SIZE = 5;
  const FAST_SCROLL_THRESHOLD = 80;      // Increased for less sensitivity
  const CONTINUOUS_SCROLL_THRESHOLD = 30; // Slightly increased
  const STABILITY_DELAY = 250;           // Increased for better stability
  const MAX_CONTINUOUS_SCROLLS = 6;      // Reduced for quicker response

  // DOM elements
  const smartSection = document.querySelector('.smart-features-section');
  const cardsContainer = document.querySelector('.cards-container');
  const cards = document.querySelectorAll('.feature-card');
  const indicators = document.querySelectorAll('.indicator');
  const prevButton = document.querySelector('.prev-arrow');
  const nextButton = document.querySelector('.next-arrow');
  const completionIndicator = document.querySelector('.completion-indicator');

  if (!smartSection || !cardsContainer || !cards.length) {
    console.error('❌ Smart Features elements not found');
    return;
  }

  console.log(`✅ Found ${cards.length} cards, ${indicators.length} indicators`);

  // Initialize videos
  function initializeVideos() {
    const videos = document.querySelectorAll('.smart-features-section video');
    videos.forEach((video, index) => {
      video.addEventListener('loadeddata', () => {
        console.log(`📹 Video ${index} loaded successfully`);
      });

      video.addEventListener('error', (e) => {
        console.error(`❌ Video ${index} failed to load:`, e);
      });

      // Set video properties
      video.muted = true;
      video.loop = true;
      video.playsInline = true;
    });
  }

  // Update card display with smooth animations
  function updateCardDisplay(direction = 'next') {
    if (isAnimating) return;

    isAnimating = true;

    // Remove all show-card classes
    cardsContainer.classList.remove('show-card-0', 'show-card-1', 'show-card-2', 'show-card-3', 'show-card-4');

    // Add current card class
    cardsContainer.classList.add(`show-card-${currentCardIndex}`);

    // Update active states
    cards.forEach((card, index) => {
      card.classList.toggle('active', index === currentCardIndex);
    });

    // Update indicators
    indicators.forEach((indicator, index) => {
      indicator.classList.toggle('active', index === currentCardIndex);
    });

    // Mark card as viewed
    viewedCards.add(currentCardIndex);

    // Control video playback
    controlVideoPlayback();

    console.log(`🎨 Updated display to card ${currentCardIndex}, viewed: [${Array.from(viewedCards).join(', ')}]`);

    // Reset animation flag after transition
    setTimeout(() => {
      isAnimating = false;
    }, CONFIG.ANIMATION_DURATION);

    // Check if all 5 cards have been viewed (unlock after 5 views)
    if (viewedCards.size >= 5) {
      console.log('🎯 All 5 cards viewed! viewedCards.size:', viewedCards.size);
      showCompletionIndicator();
      if (CONFIG.SCROLL_LOCK_ENABLED && !isDelayedUnlockInProgress) {
        console.log('🕐 Starting 2.5 second delayed unlock...');
        console.log('🔒 Current scroll lock state:', isScrollLocked);
        isDelayedUnlockInProgress = true;
        delayedUnlockTimer = setTimeout(() => {
          console.log('⏰ 2.5 second delay completed, unlocking scroll');
          console.log('🔓 About to call unlockScroll() after delay');
          isDelayedUnlockInProgress = false;
          delayedUnlockTimer = null;
          unlockScroll();
        }, 1500); // 2.5 second delay before unlocking
      } else {
        console.log('❌ Delayed unlock not started. SCROLL_LOCK_ENABLED:', CONFIG.SCROLL_LOCK_ENABLED, 'isDelayedUnlockInProgress:', isDelayedUnlockInProgress);
      }
    }
  }

  // Control video playback
  function controlVideoPlayback() {
    cards.forEach((card, index) => {
      const video = card.querySelector('video');
      if (video) {
        if (index === currentCardIndex) {
          video.play().catch(e => console.log('Video autoplay prevented:', e));
        } else {
          video.pause();
        }
      }
    });
  }

  // Navigate to specific card
  function navigateToCard(index, direction = 'next') {
    if (index < 0 || index >= CONFIG.TOTAL_CARDS || index === currentCardIndex || isAnimating) {
      return;
    }

    currentCardIndex = index;
    updateCardDisplay(direction);
    resetAutoPlay();
  }

  // Navigate to next card
  function nextCard() {
    const nextIndex = (currentCardIndex + 1) % CONFIG.TOTAL_CARDS;
    navigateToCard(nextIndex, 'next');
  }

  // Navigate to previous card
  function prevCard() {
    const prevIndex = (currentCardIndex - 1 + CONFIG.TOTAL_CARDS) % CONFIG.TOTAL_CARDS;
    navigateToCard(prevIndex, 'prev');
  }

  // Show completion indicator
  function showCompletionIndicator() {
    if (completionIndicator) {
      completionIndicator.style.display = 'block';
      setTimeout(() => {
        completionIndicator.classList.add('show');
      }, 100);
      console.log('✅ All cards viewed - completion indicator shown');
    }
  }

  // Auto-play functionality
  function startAutoPlay() {
    if (autoPlayTimer) clearTimeout(autoPlayTimer);

    autoPlayTimer = setTimeout(() => {
      if (isInSection && !isScrollLocked) {
        nextCard();
        startAutoPlay();
      }
    }, CONFIG.AUTO_PLAY_DELAY);
  }

  function resetAutoPlay() {
    if (autoPlayTimer) {
      clearTimeout(autoPlayTimer);
      autoPlayTimer = null;
    }
    if (isInSection && !isScrollLocked) {
      startAutoPlay();
    }
  }

  function stopAutoPlay() {
    if (autoPlayTimer) {
      clearTimeout(autoPlayTimer);
      autoPlayTimer = null;
    }
  }



  // Wheel event handler
  function handleWheel(event) {
    if (!isInSection || !CONFIG.WHEEL_ENABLED || isAnimating) return;

    event.preventDefault();

    const now = Date.now();
    const deltaY = event.deltaY;

    // Debounce wheel events
    if (now - lastWheelTime < CONFIG.DEBOUNCE_DELAY) return;
    lastWheelTime = now;

    // Accumulate wheel delta
    wheelDelta += deltaY;

    if (Math.abs(wheelDelta) > CONFIG.WHEEL_THRESHOLD) {
      if (wheelDelta > 0) {
        nextCard();
      } else {
        prevCard();
      }
      wheelDelta = 0;
    }
  }

  // Touch event handlers
  function handleTouchStart(event) {
    if (!CONFIG.TOUCH_ENABLED) return;

    const touch = event.touches[0];
    touchStartX = touch.clientX;
    touchStartY = touch.clientY;
  }

  function handleTouchEnd(event) {
    if (!CONFIG.TOUCH_ENABLED || isAnimating) return;

    const touch = event.changedTouches[0];
    touchEndX = touch.clientX;
    touchEndY = touch.clientY;

    const deltaX = touchEndX - touchStartX;
    const deltaY = touchEndY - touchStartY;

    // Only handle horizontal swipes
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
      if (deltaX > 0) {
        prevCard();
      } else {
        nextCard();
      }
    }
  }

  // Keyboard navigation
  function handleKeyboard(event) {
    if (!isInSection || !CONFIG.KEYBOARD_ENABLED) return;

    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault();
        prevCard();
        break;
      case 'ArrowRight':
        event.preventDefault();
        nextCard();
        break;
      case 'Escape':
        if (isScrollLocked) {
          unlockScroll();
        }
        break;
    }
  }

  // Event listeners setup
  function setupEventListeners() {
    // Button navigation
    if (prevButton) {
      prevButton.addEventListener('click', prevCard);
    }

    if (nextButton) {
      nextButton.addEventListener('click', nextCard);
    }

    // Indicator navigation
    indicators.forEach((indicator, index) => {
      indicator.addEventListener('click', () => {
        navigateToCard(index);
      });
    });

    // Keyboard navigation
    if (CONFIG.KEYBOARD_ENABLED) {
      document.addEventListener('keydown', handleKeyboard);
    }

    // Wheel navigation
    if (CONFIG.WHEEL_ENABLED) {
      smartSection.addEventListener('wheel', handleWheel, { passive: false });
    }

    // Touch navigation
    if (CONFIG.TOUCH_ENABLED) {
      smartSection.addEventListener('touchstart', handleTouchStart, { passive: true });
      smartSection.addEventListener('touchend', handleTouchEnd, { passive: true });
    }

    // Scroll detection will be initialized in initialize() function

    // Mouse events for auto-play control
    smartSection.addEventListener('mouseenter', () => {
      stopAutoPlay();
    });

    smartSection.addEventListener('mouseleave', () => {
      if (isInSection && !isScrollLocked) {
        startAutoPlay();
      }
    });
  }

  // Create scroll lock indicator
  function createScrollLockIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'scroll-lock-indicator';
    indicator.textContent = 'Explore all features to continue';
    document.body.appendChild(indicator);
    return indicator;
  }

  // Show/hide scroll lock indicator
  function showScrollLockIndicator() {
    let indicator = document.querySelector('.scroll-lock-indicator');
    if (!indicator) {
      indicator = createScrollLockIndicator();
    }

    setTimeout(() => {
      indicator.classList.add('show');
    }, 300);
  }

  function hideScrollLockIndicator() {
    const indicator = document.querySelector('.scroll-lock-indicator');
    if (indicator) {
      indicator.classList.remove('show');
      setTimeout(() => {
        if (indicator.parentNode) {
          indicator.parentNode.removeChild(indicator);
        }
      }, 500);
    }
  }

  // Pure JavaScript scroll lock system based on ir3-v2-key-features stable implementation
  let scrollTriggerObserver = null;
  let scrollLockPosition = null;

  // Pure JavaScript scroll lock system based on ir3-v2-key-features stable implementation
  function lockScroll() {
    if (isScrollLocked) return;

    console.log('🔒 Enabling scroll lock with ir3-v2-key-features method');
    isScrollLocked = true;

    // Store current scroll position
    scrollLockPosition = window.scrollY;
    const rect = smartSection.getBoundingClientRect();

    // Ensure component is at the top (based on ir3-v2-key-features logic)
    if (Math.abs(rect.top) > 20) {
      console.log('📍 Adjusting position for optimal lock');
      scrollLockPosition = window.scrollY + rect.top;
      window.scrollTo(0, scrollLockPosition);
    }

    // Apply scroll lock (exact same method as ir3-v2-key-features)
    document.body.style.position = 'fixed';
    document.body.style.top = `-${scrollLockPosition}px`;
    document.body.style.left = '0';
    document.body.style.right = '0';
    document.body.style.width = '100%';
    document.body.style.overflow = 'hidden';

    // Add scroll event listeners
    document.addEventListener('wheel', handleScrollInSection, { passive: false });
    document.addEventListener('touchmove', handleTouchInSection, { passive: false });
    document.addEventListener('keydown', handleKeyInSection, { passive: false });

    // Add visual indicator
    document.body.classList.add('scroll-locked');
    smartSection.classList.add('scroll-locked');

    console.log('🔒 Scroll locked at position:', scrollLockPosition);

    // Show visual indicator with animation
    showScrollLockIndicator();

    console.log('✅ GSAP ScrollTrigger pin activated with precision controls');
  }

  // Handle scroll events while section is locked (optimized for smart-features)
  function handleScrollInSection(e) {
    if (viewedCards.size >= CONFIG.TOTAL_CARDS) {
      // If delayed unlock is in progress, don't immediately unlock
      if (isDelayedUnlockInProgress) {
        console.log('⏳ All cards viewed, but delayed unlock in progress - waiting...');
        return;
      }
      console.log('🔓 All cards viewed, allowing natural scroll');
      unlockScroll();
      return;
    }

    if (!isScrollLocked || scrollCooldown || isAnimating) {
      if (isAnimating) {
        console.log('⏳ Animation in progress, blocking scroll');
      }
      return;
    }

    e.preventDefault();
    e.stopPropagation();

    // Improved throttle with faster response
    const now = Date.now();
    if (now - lastScrollTime < 300) return;

    const delta = e.deltaY;
    const absDelta = Math.abs(delta);

    // Ignore very small movements but be more responsive
    if (absDelta < 8) return;

    lastScrollTime = now;
    scrollCooldown = true;

    console.log(`🎮 Smart Features scroll: delta=${delta}, currentIndex=${currentCardIndex}`);

    if (delta > 0) {
      // Scrolling down - next card
      if (currentCardIndex < CONFIG.TOTAL_CARDS - 1) {
        navigateToCard(currentCardIndex + 1, 'next');
      } else {
        // At last card, check if all viewed
        if (viewedCards.size >= CONFIG.TOTAL_CARDS) {
          // If delayed unlock is in progress, don't immediately unlock
          if (isDelayedUnlockInProgress) {
            console.log('⏳ At last card, all viewed, but delayed unlock in progress - waiting...');
            return;
          }
          console.log('🔓 All cards viewed, allowing scroll unlock');
          unlockScroll();
        }
      }
    } else {
      // Scrolling up - previous card
      if (currentCardIndex > 0) {
        navigateToCard(currentCardIndex - 1, 'prev');
      } else {
        // At first card - allow exit if all cards viewed
        if (viewedCards.size >= CONFIG.TOTAL_CARDS) {
          // If delayed unlock is in progress, don't immediately unlock
          if (isDelayedUnlockInProgress) {
            console.log('⏳ At first card, all viewed, but delayed unlock in progress - waiting...');
            return;
          }
          console.log('🔓 All cards viewed, allowing scroll up to exit');
          unlockScroll();
        }
      }
    }

    // Reset cooldown with optimized timing
    setTimeout(() => {
      scrollCooldown = false;
    }, 1200);
  }

  function handleTouchInSection(e) {
    if (!isInSection || !isScrollLocked) return;
    e.preventDefault();
  }

  function handleKeyInSection(e) {
    if (!isInSection || !isScrollLocked) return;

    if (e.key === 'ArrowDown' || e.key === 'PageDown') {
      e.preventDefault();
      if (currentCardIndex < CONFIG.TOTAL_CARDS - 1) {
        navigateToCard(currentCardIndex + 1, 'next');
      }
    } else if (e.key === 'ArrowUp' || e.key === 'PageUp') {
      e.preventDefault();
      if (currentCardIndex > 0) {
        navigateToCard(currentCardIndex - 1, 'prev');
      }
    }
  }

  // Pure JavaScript scroll unlock system based on ir3-v2-key-features stable implementation
  function unlockScroll() {
    if (!isScrollLocked) return;

    // 添加调试信息来追踪解锁调用
    console.log('🔓 UNLOCK CALLED - isDelayedUnlockInProgress:', isDelayedUnlockInProgress);
    console.log('🔓 Disabling scroll lock with ir3-v2-key-features method');
    isScrollLocked = false;

    // Remove scroll event listeners
    document.removeEventListener('wheel', handleScrollInSection, { passive: false });
    document.removeEventListener('touchmove', handleTouchInSection, { passive: false });
    document.removeEventListener('keydown', handleKeyInSection, { passive: false });

    // Restore scroll position and remove lock (exact same method as ir3-v2-key-features)
    document.body.style.position = '';
    document.body.style.top = '';
    document.body.style.left = '';
    document.body.style.right = '';
    document.body.style.width = '';
    document.body.style.overflow = '';

    // Remove visual indicators
    document.body.classList.remove('scroll-locked');
    smartSection.classList.remove('scroll-locked');

    // Restore scroll position
    if (scrollLockPosition !== null) {
      window.scrollTo(0, scrollLockPosition);
      scrollLockPosition = null;
    }

    // Hide visual indicator
    hideScrollLockIndicator();

    console.log('🔓 Scroll lock removed and position restored');
  }

  // Scroll detection system based on ir3-v2-key-features stable implementation
  function setupScrollDetection() {
    // Variables for scroll detection (based on ir3-v2-key-features)
    let scrollTimer = null;
    let lastScrollTime = 0;
    let scrollHistory = [];
    let isInSection = false;
    let hasViewedAllCards = false;

    // Scroll detection function (simplified version of ir3-v2-key-features logic)
    function handleScrollDetection() {
      const rect = smartSection.getBoundingClientRect();
      const currentScrollY = window.scrollY;
      const now = Date.now();

      // Check if section is visible and near top
      const isSectionVisible = rect.bottom > 0 && rect.top < window.innerHeight;
      const isNearTop = rect.top <= 100 && rect.top >= -100;
      const isApproachingSection = rect.top <= window.innerHeight * 0.8 && rect.top > 0;

      // Update viewed cards status
      hasViewedAllCards = viewedCards.size >= CONFIG.TOTAL_CARDS;

      // Basic conditions for scroll lock
      const basicConditionsMet = isApproachingSection && isSectionVisible && isNearTop && !isScrollLocked;

      if (basicConditionsMet && !hasViewedAllCards) {
        isInSection = true;
        console.log('✅ Conditions met, attempting scroll lock');

        clearTimeout(scrollTimer);
        scrollTimer = setTimeout(() => {
          if (isInSection && !hasViewedAllCards && !isScrollLocked) {
            console.log('🔒 Executing scroll lock attempt');
            attemptScrollLock();
          }
        }, 50); // Fast response
      } else {
        if (isInSection) {
          console.log('❌ Conditions no longer met, resetting isInSection');
          isInSection = false;
        }

        // Check if we should unlock
        if ((!isSectionVisible || !isNearTop) && isScrollLocked && hasViewedAllCards) {
          // If delayed unlock is in progress, don't immediately unlock
          if (isDelayedUnlockInProgress) {
            console.log('⏳ Section out of range, all viewed, but delayed unlock in progress - waiting...');
            return;
          }
          console.log('🔓 Section out of range and all cards viewed, unlocking');
          unlockScroll();
        }
      }
    }

    // Attempt scroll lock with positioning
    function attemptScrollLock() {
      if (isScrollLocked || hasViewedAllCards) {
        console.log('⚠️ Lock attempt aborted:', { isScrollLocked, hasViewedAllCards });
        return;
      }

      console.log('🔒 Attempting scroll lock...');
      lockScroll();
      startAutoPlay();
    }

    // Add scroll event listener
    window.addEventListener('scroll', handleScrollDetection, { passive: true });

    console.log('✅ Scroll detection initialized');
  }

  // Setup scroll appearance animations with scroll lock compatibility
  function setupScrollAnimations() {
    // Get the main section and its elements
    const section = document.querySelector('.smart-features-section');
    const title = section.querySelector('.smart-features-title');
    const subtitle = section.querySelector('.subtitle');
    const cardsCarousel = section.querySelector('.cards-carousel');
    const navigationControls = section.querySelector('.navigation-controls');
    const progressIndicators = section.querySelector('.progress-indicators');

    // 检查组件是否已经在视口中
    const rect = section.getBoundingClientRect();
    const isAlreadyVisible = rect.top < window.innerHeight * 0.8;

    if (isAlreadyVisible) {
      // 如果已经在视口中，直接显示所有元素
      [title, subtitle, cardsCarousel, navigationControls, progressIndicators].forEach(element => {
        if (element) {
          element.style.opacity = '1';
          element.style.transform = 'translateY(0px)';
          element.style.visibility = 'visible';
        }
      });
      console.log('✅ Smart Features already visible, showing immediately');
      return;
    }

    // 设置初始隐藏状态
    [title, subtitle, cardsCarousel].forEach(element => {
      if (element) {
        element.style.opacity = '0';
        element.style.transform = 'translateY(50px)';
        element.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
      }
    });

    [navigationControls, progressIndicators].forEach(element => {
      if (element) {
        element.style.opacity = '0';
        element.style.transition = 'opacity 0.5s ease-out';
      }
    });

    // 创建 ScrollTrigger 动画，但不使用 pin 功能避免冲突
    if (typeof gsap !== 'undefined' && typeof ScrollTrigger !== 'undefined') {
      ScrollTrigger.create({
        trigger: section,
        start: 'top 80%',
        onEnter: () => {
          // 依次显示元素
          setTimeout(() => {
            if (title) {
              title.style.opacity = '1';
              title.style.transform = 'translateY(0px)';
            }
          }, 0);

          setTimeout(() => {
            if (subtitle) {
              subtitle.style.opacity = '1';
              subtitle.style.transform = 'translateY(0px)';
            }
          }, 200);

          setTimeout(() => {
            if (cardsCarousel) {
              cardsCarousel.style.opacity = '1';
              cardsCarousel.style.transform = 'translateY(0px)';
            }
          }, 400);

          setTimeout(() => {
            [navigationControls, progressIndicators].forEach(element => {
              if (element) {
                element.style.opacity = '1';
              }
            });
          }, 600);

          console.log('✅ Smart Features scroll animation triggered');
        },
        once: true // 只触发一次
      });
    } else {
      // 如果 GSAP 不可用，直接显示
      [title, subtitle, cardsCarousel, navigationControls, progressIndicators].forEach(element => {
        if (element) {
          element.style.opacity = '1';
          element.style.transform = 'translateY(0px)';
        }
      });
    }

    console.log('✅ Smart Features scroll animations setup complete');
  }

  // Initialize everything
  function initialize() {
    console.log('🎬 Initializing Smart Features...');

    initializeVideos();
    updateCardDisplay();
    setupEventListeners();
    setupScrollDetection(); // Use new scroll detection system
    setupScrollAnimations(); // Add scroll appearance animations

    console.log('✅ Smart Features initialized successfully');
  }

  // Mobile horizontal scroll functionality
  function initializeMobileScroll() {
    if (window.innerWidth > 768) return; // Only for mobile

    const cardFeatures = document.querySelectorAll('.card-features');

    cardFeatures.forEach(container => {
      if (!container) return;

      const items = container.querySelectorAll('li');
      if (items.length === 0) return;

      // Clone items for seamless loop
      items.forEach(item => {
        const clone = item.cloneNode(true);
        container.appendChild(clone);
      });

      let scrollPosition = 0;
      const itemWidth = items[0].offsetWidth + 8; // item width + gap
      const totalWidth = itemWidth * items.length;
      let isScrolling = false;

      // Auto scroll function
      function autoScroll() {
        if (isScrolling) return;

        scrollPosition += 1;
        container.scrollLeft = scrollPosition;

        // Reset to beginning for seamless loop
        if (scrollPosition >= totalWidth) {
          setTimeout(() => {
            container.scrollLeft = 0;
            scrollPosition = 0;
          }, 100);
        }

        requestAnimationFrame(autoScroll);
      }

      // Pause auto scroll on user interaction
      container.addEventListener('touchstart', () => {
        isScrolling = true;
      });

      container.addEventListener('touchend', () => {
        setTimeout(() => {
          isScrolling = false;
        }, 2000); // Resume after 2 seconds
      });

      container.addEventListener('scroll', () => {
        if (!isScrolling) {
          scrollPosition = container.scrollLeft;
        }
      });

      // Start auto scroll
      autoScroll();
    });
  }

  // Initialize mobile scroll on load and resize
  initializeMobileScroll();
  window.addEventListener('resize', initializeMobileScroll);

  // Start initialization
  initialize();
});