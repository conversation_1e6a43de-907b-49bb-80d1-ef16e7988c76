.ir3-batch-printing-video {
  position: relative;
  background: #ffffff;
  padding: 120px 0;
  height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  box-sizing: border-box;
}

/* Background Effects */
.batch-background-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.batch-floating-shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(66, 165, 245, 0.03) 0%, rgba(29, 233, 182, 0.02) 100%);
  filter: blur(40px);
  opacity: 0.6;
}

.batch-shape-1 {
  width: 300px;
  height: 300px;
  top: 10%;
  left: 5%;
  animation: float-slow 20s infinite ease-in-out alternate;
}

.batch-shape-2 {
  width: 200px;
  height: 200px;
  bottom: 15%;
  right: 10%;
  animation: float-slow 25s infinite ease-in-out alternate-reverse;
}

.batch-shape-3 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 30%;
  animation: float-slow 15s infinite ease-in-out alternate;
}

@keyframes float-slow {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  50% {
    transform: translateY(-20px) translateX(10px);
  }
}

/* Main Container */
.batch-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  position: relative;
  z-index: 2;
}

/* Content Grid */
.batch-content-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 80px;
  align-items: center;
  min-height: 500px;
  width: 100%;
}

/* Left Content */
.batch-content-left {
  padding: 40px 40px;
  height: auto;
}

.batch-text-content {
  max-width: none;
}

/* Title - Hero Section Style */
.batch-title {
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  font-weight: 900;
  line-height: 1.2;
  margin: 0 0 2rem 0;
  letter-spacing: -0.02em;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
  display: flex;
  flex-direction: column;
}

.title-line-1 {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: block;
}

.title-line-2 {
  background: linear-gradient(135deg, #f59e0b 0%, #ef4444 50%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: block;
}

/* Description - Hero Section Style */
.batch-description {
  font-size: clamp(1.1rem, 2vw, 1.25rem);
  line-height: 1.7;
  color: #475569;
  margin: 0 0 3rem 0;
  font-weight: 450;
  max-width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  letter-spacing: -0.01em;
}

/* Bottom Features Section */
.batch-bottom-features {
  margin-top: 0;
  padding: 0 20px;
  background: transparent;
  position: relative;
}

.batch-features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 48px;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 24px;
  padding: 36px 28px;
  font-size: clamp(1rem, 1.6vw, 1.125rem);
  color: #334155;
  font-weight: 600;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  border-radius: 24px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  letter-spacing: -0.01em;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.feature-item:hover {
  transform: translateY(-12px) scale(1.02);
}

.feature-item:hover .feature-icon {
  transform: scale(1.15) rotate(5deg);
}

.feature-item span {
  font-weight: 600;
  line-height: 1.4;
  transition: all 0.3s ease;
}

.feature-item:hover span {
  color: #1e293b;
  font-weight: 700;
}

/* 添加闪烁动画效果 */
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.feature-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%);
  transition: all 0.6s ease;
  z-index: 1;
}

.feature-item:hover::after {
  animation: shimmer 0.8s ease-in-out;
}

.feature-item:hover .feature-icon {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(66, 165, 245, 0.3);
}

.feature-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 52px;
  height: 52px;
  border-radius: 16px;
  color: white;
  flex-shrink: 0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* 统一蓝色主题特性 */
.feature-item .feature-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.feature-item {
  background: transparent;
}

.feature-item:hover {
  box-shadow: 0 15px 35px rgba(59, 130, 246, 0.15);
}

.feature-item:hover .feature-icon {
  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
}

/* Right Video */
.batch-content-right {
  position: relative;
  height: auto;
  overflow: hidden;
}

.batch-video-container {
  position: relative;
  width: 100%;
  max-width: none;
  margin: 0 auto;
}

.video-wrapper {
  position: relative;
  width: 100%;
  aspect-ratio: 1/1;
  overflow: hidden;
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.batch-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
  border-radius: 16px;
  background: white;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.batch-video:hover {
  transform: scale(1.02);
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
  .ir3-batch-printing-video {
    height: auto;
    min-height: 100vh;
    padding: 80px 0;
  }

  .batch-content-grid {
    grid-template-columns: 1fr;
    gap: 50px;
    min-height: auto;
  }

  .batch-content-left {
    padding: 0;
    height: auto;
    order: 2;
  }

  .batch-content-right {
    order: 1;
    height: auto;
  }

  .batch-title {
    font-size: 40px;
  }

  .batch-description {
    font-size: 18px;
    max-width: 100%;
  }

  .batch-container {
    padding: 0 20px;
  }

  .batch-bottom-features {
    margin-top: 20px;
    padding: 20px 20px;
  }

  .batch-features-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    max-width: 100%;
  }

  .feature-item {
    padding: 20px 12px;
    font-size: 0.9rem;
    text-align: center;
  }

  .feature-icon {
    width: 50px;
    height: 50px;
    margin: 0 auto 12px;
  }

  .feature-text {
    font-size: 14px;
    line-height: 1.3;
  }
}

@media (max-width: 480px) {
  .ir3-batch-printing-video {
    padding: 60px 0;
  }

  .batch-title {
    font-size: 28px;
    line-height: 1.2;
  }

  .batch-description {
    font-size: 15px;
    line-height: 1.5;
  }

  .batch-container {
    padding: 0 16px;
  }

  .batch-bottom-features {
    margin-top: -35px;
    padding: 20px 16px;
  }

  .batch-features-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .feature-item {
    padding: 16px 8px;
  }

  .feature-icon {
    width: 40px;
    height: 40px;
    margin: 0 auto 8px;
  }

  .feature-text {
    font-size: 12px;
    line-height: 1.2;
  }

  .batch-video {
    height: 250px;
  }
}
