// IR3 Video Scroll Component JavaScript
class IR3VideoScroll {
  constructor(sectionId) {
    this.sectionId = sectionId;
    this.section = document.querySelector(`#ir3-video-scroll-${sectionId}`);
    this.video = null;
    this.contentWrapper = null;
    this.isVideoLoaded = false;
    this.scrollTriggerInstance = null;
    this.contentAnimationInstance = null;
    this.loadingElement = null;
    
    if (!this.section) {
      console.warn(`IR3 Video Scroll: Section with ID ${sectionId} not found`);
      return;
    }
    
    this.init();
  }

  init() {
    this.setupElements();
    this.checkGSAP();
    this.setupIntersectionObserver();
    this.bindEvents();
    
    console.log(`IR3 Video Scroll component initialized for section: ${this.sectionId}`);
  }

  setupElements() {
    this.video = this.section.querySelector('.background-video');
    this.contentWrapper = this.section.querySelector('.content-wrapper');
    this.loadingElement = this.section.querySelector('.video-loading');
    
    if (!this.video || !this.contentWrapper) {
      console.warn('IR3 Video Scroll: Required elements not found');
      return;
    }
  }

  checkGSAP() {
    if (typeof gsap === 'undefined') {
      console.warn('GSAP not loaded for IR3 Video Scroll');
      this.loadGSAPFallback();
      return false;
    }
    
    if (typeof ScrollTrigger === 'undefined') {
      gsap.registerPlugin(ScrollTrigger);
    }
    
    return true;
  }

  loadGSAPFallback() {
    // 简单的fallback动画，不依赖GSAP
    this.contentWrapper.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.contentWrapper.style.opacity = '1';
          this.contentWrapper.style.transform = 'translateY(0)';
        }
      });
    }, { threshold: 0.3 });
    
    observer.observe(this.section);
  }

  setupIntersectionObserver() {
    console.log('🔍 设置 IntersectionObserver');

    // 立即初始化视频和动画，不等待 IntersectionObserver
    console.log('⚡ 立即初始化视频和动画');
    this.initVideo();
    this.createAnimations();

    // 保留 IntersectionObserver 作为备用
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        console.log('👁️ IntersectionObserver 触发:', entry.isIntersecting);
        if (entry.isIntersecting) {
          console.log('✅ 组件进入视口，但动画已经初始化');
          observer.unobserve(entry.target);
        }
      });
    }, {
      rootMargin: '50px'
    });

    observer.observe(this.section);
  }

  initVideo() {
    if (!this.video) return;

    // 重置视频状态
    this.video.currentTime = 0;
    this.video.pause();
    
    // 添加加载事件监听器
    this.video.addEventListener('loadedmetadata', () => {
      this.isVideoLoaded = true;
      this.onVideoLoaded();
    });

    this.video.addEventListener('error', (e) => {
      console.error('Video loading error:', e);
      this.handleVideoError();
    });

    // 确保视频源正确设置
    const videoSrc = this.video.dataset.videoSrc;
    if (videoSrc && this.video.src !== videoSrc) {
      this.video.src = videoSrc;
      this.video.load();
    }
  }

  onVideoLoaded() {
    console.log(`Video loaded successfully. Duration: ${this.video.duration}s`);
    this.removeLoadingState();
  }

  handleVideoError() {
    console.error('Failed to load video');
    this.section.classList.add('no-video');
    this.removeLoadingState();
  }

  removeLoadingState() {
    if (this.loadingElement) {
      this.loadingElement.style.opacity = '0';
      setTimeout(() => {
        if (this.loadingElement && this.loadingElement.parentNode) {
          this.loadingElement.parentNode.removeChild(this.loadingElement);
        }
      }, 300);
    }
  }

  createAnimations() {
    console.log('🚀 createAnimations 开始执行');
    if (!this.checkGSAP()) {
      console.log('❌ GSAP 检查失败，动画创建中止');
      return;
    }

    console.log('✅ GSAP 检查通过，开始创建动画');
    this.createVideoScrollAnimation();
    this.createContentAnimation();
    this.createGradientMaskAnimation();
    this.setupScrollTriggerRefresh();
    console.log('🎉 所有动画创建完成');
  }

  createVideoScrollAnimation() {
    if (!this.video) return;

    this.scrollTriggerInstance = ScrollTrigger.create({
      trigger: this.section,
      start: 'top bottom',
      end: 'bottom top',
      scrub: 1.2, // 平滑的scrub效果
      invalidateOnRefresh: true,
      onUpdate: (self) => {
        if (this.isVideoLoaded && this.video.duration) {
          // 使用easing函数使视频播放更平滑
          const progress = this.easeInOutQuad(self.progress);
          const targetTime = progress * this.video.duration;
          
          // 防抖处理，避免频繁更新
          if (!this.updateTimeout) {
            this.updateTimeout = requestAnimationFrame(() => {
              this.video.currentTime = targetTime;
              this.updateTimeout = null;
            });
          }
        }
      },
      onEnter: () => {
        console.log('进入视频滚动区域');
        this.section.classList.add('video-active');
      },
      onLeave: () => {
        console.log('离开视频滚动区域');
        this.section.classList.remove('video-active');
      }
    });
  }

  createContentAnimation() {
    if (!this.contentWrapper) return;

    const title = this.contentWrapper.querySelector('.video-title');
    const description = this.contentWrapper.querySelector('.video-description');
    const videoOverlay = this.section.querySelector('.video-overlay');

    // 创建分阶段的内容动画，包含渐变遮罩
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: this.section,  // 触发元素：视频组件区域
        /* 🎯 滚动触发位置控制：控制滚动到哪里开始显示标题和描述 */
        start: 'top 60%',       // ⚙️ 开始位置：当组件顶部到达视口80%位置时开始动画
                                // 调整说明：改为'top 90%'让动画更早触发，改为'top 60%'让动画更晚触发
        end: 'center center',   // ⚙️ 结束位置：当组件中心到达视口中心时动画完成
                                // 调整说明：改为'bottom top'让动画区间更长，改为'top bottom'让动画区间更短
        /* 🕐 滚动同步速度控制：控制动画与滚动的绑定程度 */
        scrub: 1.5,             // ⚙️ 滚动绑定：1.5秒的缓冲，数值越大动画越平滑但响应越慢
                                // 调整说明：改为1让响应更快，改为3让动画更平滑
        invalidateOnRefresh: true,
        onStart: () => {
          console.log('Klipper 内容动画开始 - 包含渐变遮罩');
        },
        onLeave: () => {
          console.log('离开视频区域 - 渐变遮罩淡出');
        },
        onEnterBack: () => {
          console.log('重新进入视频区域 - 渐变遮罩淡入');
        }
      }
    });

    // 第一阶段：渐变遮罩淡入（最早开始，为文字提供背景）
    if (videoOverlay) {
      tl.to(videoOverlay, {
        '--gradient-opacity': 1, // 使用 CSS 自定义属性控制渐变遮罩
        /* 🕐 渐变遮罩动画时间控制：duration=400ms, delay=0ms */
        duration: 0.4,  // ⚙️ 手动调整参数：渐变遮罩淡入速度，增加数值让遮罩出现更慢
        ease: 'power2.out'
      }, 0);  // ⚙️ 立即开始：0ms延迟，最先执行为后续文字提供背景
    }

    // 第二阶段：容器淡入
    tl.to(this.contentWrapper, {
      opacity: 1,
      /* 🕐 容器动画时间控制：duration=300ms, delay=100ms */
      duration: 0.3,  // ⚙️ 手动调整参数：容器淡入速度，影响整体内容显示时机
      ease: 'power2.out'
    }, 0.1)  // ⚙️ 延迟时间：100ms后开始容器淡入，与遮罩形成层次感
    // 第三阶段：标题动画 - 从下方滑入并带有轻微缩放
    .fromTo(title, {
      y: 80,        // 初始位置：向下偏移80px
      opacity: 0,   // 初始透明度：完全透明
      scale: 0.95   // 初始缩放：95%大小
    }, {
      y: 0,         // 最终位置：原始位置
      opacity: 1,   // 最终透明度：完全不透明
      scale: 1,     // 最终缩放：100%大小
      /* 🕐 标题动画持续时间控制：标题从出现到完全显示需要多长时间 */
      duration: 1.5,      // ⚙️ 动画持续时间：0.8秒(800毫秒)
                          // 调整说明：改为0.5让标题出现更快，改为1.2让标题出现更慢
      ease: 'power3.out'  // 缓动函数：快速开始，缓慢结束
    }, 0.3)               // ⚙️ 动画开始延迟：滚动触发后300毫秒才开始标题动画
                          // 调整说明：改为0让标题立即出现，改为0.5让标题延迟更久
    // 第四阶段：描述文字动画 - 与渐变遮罩同步
    .fromTo(description, {
      y: 60,        // 初始位置：向下偏移60px
      opacity: 0    // 初始透明度：完全透明
    }, {
      y: 0,         // 最终位置：原始位置
      opacity: 1,   // 最终透明度：完全不透明
      /* 🕐 描述文本动画持续时间控制：描述从出现到完全显示需要多长时间 */
      duration: 1,        // ⚙️ 动画持续时间：1秒(1000毫秒)
                          // 调整说明：改为0.6让描述出现更快，改为1.5让描述出现更慢
      ease: 'power2.out'  // 缓动函数：快速开始，缓慢结束
    }, 0.7);              // ⚙️ 动画开始延迟：滚动触发后700毫秒才开始描述动画
                          // 调整说明：改为0.3让描述与标题同时出现，改为1.0让描述延迟更久

    this.contentAnimationInstance = tl;
  }

  createGradientMaskAnimation() {
    const videoOverlay = this.section.querySelector('.video-overlay');
    if (!videoOverlay) {
      console.log('❌ 渐变遮罩元素未找到');
      return;
    }

    console.log('✅ 渐变遮罩动画初始化开始');

    // 🎭 正常模式：渐变遮罩由 ScrollTrigger 控制
    console.log('🎭 正常模式：渐变遮罩由 ScrollTrigger 控制');

    // 检查组件初始位置，如果已经在视口中则立即显示遮罩
    const rect = this.section.getBoundingClientRect();
    const isInitiallyVisible = rect.top < window.innerHeight && rect.bottom > 0;

    if (isInitiallyVisible) {
      console.log('🎭 组件初始加载时已在视口中，立即显示渐变遮罩');
      videoOverlay.classList.add('mask-visible');
    }

    // 创建独立的渐变遮罩动画，与视频滚动同步
    this.gradientMaskInstance = ScrollTrigger.create({
      trigger: this.section,
      /* 🎯 触发时机控制：优化滚动触发条件 */
      start: 'top 90%',     // 当组件顶部到达视口90%位置时触发
      end: 'bottom 10%',    // 当组件底部离开视口10%位置时结束
      markers: false,       // 关闭调试标记
      toggleActions: 'play none none reverse', // 进入时播放，离开时反向
      onEnter: () => {
        console.log('🎭 渐变遮罩淡入 - ScrollTrigger onEnter');
        console.log('📍 触发位置: 组件顶部到达视口90%');
        videoOverlay.classList.add('mask-visible');
      },
      onLeave: () => {
        console.log('🎭 渐变遮罩淡出 - ScrollTrigger onLeave');
        console.log('📍 触发位置: 组件底部离开视口10%');
        videoOverlay.classList.remove('mask-visible');
      },
      onEnterBack: () => {
        console.log('🎭 渐变遮罩重新淡入 - ScrollTrigger onEnterBack');
        console.log('📍 触发位置: 向上滚动重新进入');
        videoOverlay.classList.add('mask-visible');
      },
      onLeaveBack: () => {
        console.log('🎭 渐变遮罩向上淡出 - ScrollTrigger onLeaveBack');
        console.log('📍 触发位置: 向上滚动离开');
        videoOverlay.classList.remove('mask-visible');
      },
      onRefresh: () => {
        console.log('🔄 ScrollTrigger 刷新 - 渐变遮罩');
        // 刷新时重新检查位置
        const currentRect = this.section.getBoundingClientRect();
        const isCurrentlyVisible = currentRect.top < window.innerHeight && currentRect.bottom > 0;
        if (isCurrentlyVisible) {
          videoOverlay.classList.add('mask-visible');
        }
      }
    });



    console.log('✅ 渐变遮罩动画初始化完成');
    console.log('📊 初始状态:', {
      isInitiallyVisible,
      rectTop: rect.top,
      rectBottom: rect.bottom,
      windowHeight: window.innerHeight
    });
  }



  // Easing function for smooth video playback
  easeInOutQuad(t) {
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
  }

  setupScrollTriggerRefresh() {
    // 页面加载完成后刷新ScrollTrigger
    window.addEventListener('load', () => {
      ScrollTrigger.refresh();
    });

    // 窗口大小改变时刷新
    let resizeTimeout;
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        ScrollTrigger.refresh();
      }, 250);
    });
  }

  bindEvents() {
    // 页面可见性变化时暂停/恢复视频
    document.addEventListener('visibilitychange', () => {
      if (!this.video) return;
      
      if (document.hidden) {
        this.video.pause();
      }
    });

    // 移动端触摸优化
    if ('ontouchstart' in window) {
      this.section.addEventListener('touchstart', () => {
        if (this.video && !this.isVideoLoaded) {
          this.video.load();
        }
      }, { passive: true });
    }
  }

  // 公共方法：销毁实例
  destroy() {
    if (this.scrollTriggerInstance) {
      this.scrollTriggerInstance.kill();
    }

    if (this.contentAnimationInstance) {
      this.contentAnimationInstance.kill();
    }

    if (this.gradientMaskInstance) {
      this.gradientMaskInstance.kill();
    }

    if (this.updateTimeout) {
      cancelAnimationFrame(this.updateTimeout);
    }

    console.log(`IR3 Video Scroll component destroyed for section: ${this.sectionId}`);
  }
}

// 自动初始化所有实例
document.addEventListener('DOMContentLoaded', function() {
  const videoScrollSections = document.querySelectorAll('[id^="ir3-video-scroll-"]');
  
  videoScrollSections.forEach(section => {
    const sectionId = section.id.replace('ir3-video-scroll-', '');
    new IR3VideoScroll(sectionId);
  });
});

// 导出类以供其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = IR3VideoScroll;
}
