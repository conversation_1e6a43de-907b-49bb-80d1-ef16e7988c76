/* IR3 Video Scroll Component Styles */

/* Main Container */
.ir3-video-scroll {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
}

/* Video Container */
.video-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  min-height: 500px; /* 确保有最小高度 */
  overflow: hidden;
  z-index: 1;
  background: #000; /* 确保有背景色 */
}

.background-video {
  position: absolute;
  top: 50%;
  left: 50%;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  transform: translate3d(-50%, -50%, 0);
  object-fit: cover;
  z-index: 1;
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* Video Overlay */
.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.4) 100%
  );
  z-index: 2;
  pointer-events: none;
}

/* Video Gradient Mask - 从上到下的渐变遮罩（动态控制） */
.video-overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  /* 🎛️ 渐变覆盖范围控制：调整此值改变遮罩覆盖区域大小 */
  height: 80%; /* 覆盖视频上80%区域 - 增强视觉效果 */
  background: linear-gradient(
    to bottom,
    /* 🎨 遮罩黑色深度控制：调整这些透明度值改变黑色深度 */
    rgba(0, 0, 0, 1) 0%,    /* 顶部深黑色 - 强效果 */
    rgba(0, 0, 0, 1) 0%,   /* 25%处较深黑色 */
    rgba(0, 0, 0, 1) 0%,   /* 中间位置中等黑色 */
    rgba(0, 0, 0, 1) 0%,   /* 75%处较浅黑色 */
    rgba(0, 0, 0, 0) 100%     /* 底部完全透明 */
  );
  /* ⚡ 初始状态控制：调整此值改变遮罩初始显示状态 */
  opacity: 0; /* 初始完全隐藏 */
  z-index: 3; /* 提高层级确保显示在视频上方 */
  pointer-events: none;
  will-change: opacity; /* 性能优化 */
  /* 🕐 CSS过渡时长控制：调整此值改变CSS层面的过渡速度 */
  transition: opacity 1.5s ease-in-out; /* 平滑过渡效果 */
}

/* 🎭 遮罩可见状态控制：通过类名控制遮罩显示 */
.video-overlay.mask-visible::after {
  opacity: 1 !important; /* 显示状态 - 完全可见，使用 !important 覆盖内联样式 */
}

/* 🎭 遮罩可见状态：控制渐变遮罩显示 */
.video-overlay.mask-visible::after {
  opacity: 1 !important; /* 显示渐变遮罩 */
}

/* 🎭 遮罩容器：确保有足够的尺寸 */
.video-overlay.mask-visible {
  min-height: 400px; /* 确保有足够高度 */
}

/* Content Container */
.content-container {
  position: relative;
  z-index: 3;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 5%;
}

.content-wrapper {
  text-align: center;
  max-width: 1000px; /* 增加最大宽度为标题提供更多空间 */
  opacity: 0;
  transform: translateY(60px);
  will-change: transform, opacity;
}

/* Typography - Apple Style */
.video-title {
  font-size: clamp(2.2rem, 4.5vw, 3.8rem); /* 桌面端字体大小 */
  font-weight: 700;
  line-height: 1.1;
  margin: 0 0 2rem 0;
  letter-spacing: -0.01em;
  text-shadow: 0 4px 30px rgba(0, 0, 0, 0.5);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: linear-gradient(135deg, currentColor 0%, rgba(255, 255, 255, 0.8) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  /* 移除强制单行显示，允许换行 */
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  max-width: 100%;
}

.video-description {
  font-size: clamp(1.1rem, 2.2vw, 1.5rem);
  font-weight: 400;
  line-height: 1.6;
  margin: 0;
  opacity: 0.95;
  text-shadow: 0 2px 20px rgba(0, 0, 0, 0.6);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  max-width: 700px;
  margin: 0 auto;
  /* 优化文本换行 */
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Loading State */
.video-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 4;
  color: white;
  font-size: 1.2rem;
  opacity: 0.8;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Tablet Styles */
@media screen and (max-width: 1024px) {
  .content-container {
    padding: 0 6%;
  }
  
  .video-title {
    margin-bottom: 1.8rem;
  }
}

/* Mobile Styles - Optimized for text readability */
@media screen and (max-width: 768px) {
  .ir3-video-scroll {
    height: auto !important;
    min-height: 100vh;
    padding: 3rem 0; /* 减少上下内边距为文本留出更多空间 */
  }

  .content-container {
    padding: 0 5%; /* 减少左右内边距，为文本提供更多宽度 */
    position: static;
    height: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh; /* 确保垂直居中 */
  }

  .content-wrapper {
    transform: translateY(0); /* 移除垂直偏移 */
    position: relative;
    z-index: 10;
    max-width: 100%; /* 确保使用全部可用宽度 */
    width: 100%;
  }

  .video-title {
    margin-bottom: 1.5rem;
    font-size: clamp(1.5rem, 6vw, 2.2rem); /* 优化移动端字体大小 */
    line-height: 1.2; /* 稍微增加行高提升可读性 */
    letter-spacing: -0.005em;
    /* 允许标题换行，确保完整显示 */
    white-space: normal;
    text-align: center;
  }

  .video-description {
    font-size: clamp(0.9rem, 3.5vw, 1.1rem); /* 优化描述文字大小 */
    line-height: 1.5; /* 增加行高提升可读性 */
    max-width: 100%; /* 使用全部可用宽度 */
    text-align: center;
    margin: 0 auto;
  }
}

/* Small Mobile Devices - Extra optimization */
@media screen and (max-width: 480px) {
  .ir3-video-scroll {
    padding: 2rem 0; /* 进一步减少内边距 */
    min-height: 100vh;
  }

  .content-container {
    padding: 0 4%; /* 最小化左右内边距 */
    min-height: 100vh;
  }

  .content-wrapper {
    max-width: 100%;
    padding: 1rem 0; /* 添加少量内边距确保文本不贴边 */
  }

  .video-title {
    margin-bottom: 1rem;
    font-size: clamp(1.3rem, 5.5vw, 1.8rem); /* 小屏幕专用字体大小 */
    line-height: 1.3; /* 优化行高 */
  }

  .video-description {
    font-size: clamp(0.85rem, 3.2vw, 1rem); /* 小屏幕专用描述字体 */
    line-height: 1.6; /* 增加行高提升可读性 */
    margin-top: 0.5rem;
  }
}

/* Extra Small Devices - iPhone SE, etc. */
@media screen and (max-width: 375px) {
  .content-container {
    padding: 0 3%; /* 最小化内边距 */
  }

  .video-title {
    font-size: clamp(1.2rem, 5vw, 1.6rem); /* 超小屏幕字体 */
    margin-bottom: 0.8rem;
  }

  .video-description {
    font-size: clamp(0.8rem, 3vw, 0.95rem); /* 超小屏幕描述字体 */
    line-height: 1.7; /* 更大行高确保可读性 */
  }
}

/* Performance Optimizations */
.ir3-video-scroll * {
  box-sizing: border-box;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .content-wrapper {
    transform: none;
    opacity: 1;
  }
  
  .background-video {
    will-change: auto;
  }
  
  .loading-spinner {
    animation: none;
  }
}

/* High DPI Displays */
@media screen and (-webkit-min-device-pixel-ratio: 2),
       screen and (min-resolution: 192dpi) {
  .video-title,
  .video-description {
    text-shadow: 0 1px 15px rgba(0, 0, 0, 0.4);
  }
}

/* Fallback for browsers without video support */
.no-video .ir3-video-scroll {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

.no-video .video-container {
  display: none;
}

/* Focus States for Accessibility */
.ir3-video-scroll:focus-within {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 4px;
}
