

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-575054661598315749.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-575054661598315749.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-575054661598315749.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-575054661598315749.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-575054661598315749.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-575054661598315749.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-575054661598315749.gps.gpsil [style*="--hvr-bgc:"]:hover{background-color:var(--hvr-bgc)}.gps-575054661598315749.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-575054661598315749.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-575054661598315749.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-575054661598315749.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-575054661598315749.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-575054661598315749.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-575054661598315749.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-575054661598315749.gps.gpsil [style*="--hvr-bb:"]:hover{border-bottom:var(--hvr-bb)}.gps-575054661598315749.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-575054661598315749.gps.gpsil [style*="--hvr-bc:"]:hover{border-color:var(--hvr-bc)}.gps-575054661598315749.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-575054661598315749.gps.gpsil [style*="--hvr-bblr:"]:hover{border-bottom-left-radius:var(--hvr-bblr)}.gps-575054661598315749.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-575054661598315749.gps.gpsil [style*="--hvr-bbrr:"]:hover{border-bottom-right-radius:var(--hvr-bbrr)}.gps-575054661598315749.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-575054661598315749.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-575054661598315749.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-575054661598315749.gps.gpsil [style*="--hvr-bs:"]:hover{border-style:var(--hvr-bs)}.gps-575054661598315749.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-575054661598315749.gps.gpsil [style*="--hvr-bt:"]:hover{border-top:var(--hvr-bt)}.gps-575054661598315749.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-575054661598315749.gps.gpsil [style*="--hvr-btlr:"]:hover{border-top-left-radius:var(--hvr-btlr)}.gps-575054661598315749.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-575054661598315749.gps.gpsil [style*="--hvr-btrr:"]:hover{border-top-right-radius:var(--hvr-btrr)}.gps-575054661598315749.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-575054661598315749.gps.gpsil [style*="--hvr-bw:"]:hover{border-width:var(--hvr-bw)}.gps-575054661598315749.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-575054661598315749.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-575054661598315749.gps.gpsil [style*="--hvr-shadow:"]:hover{box-shadow:var(--hvr-shadow)}.gps-575054661598315749.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-575054661598315749.gps.gpsil [style*="--hvr-c:"]:hover{color:var(--hvr-c)}.gps-575054661598315749.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-575054661598315749.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-575054661598315749.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-575054661598315749.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-575054661598315749.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-575054661598315749.gps.gpsil [style*="--gg:"]{grid-gap:var(--gg)}.gps-575054661598315749.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-575054661598315749.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-575054661598315749.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-575054661598315749.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-575054661598315749.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-575054661598315749.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-575054661598315749.gps.gpsil [style*="--tdt:"]{text-decoration-thickness:var(--tdt)}.gps-575054661598315749.gps.gpsil [style*="--tdc:"]{text-decoration-color:var(--tdc)}.gps-575054661598315749.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-575054661598315749.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-575054661598315749.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-575054661598315749.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-575054661598315749.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-575054661598315749.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-575054661598315749.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-575054661598315749.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-575054661598315749.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-575054661598315749.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-575054661598315749.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-575054661598315749.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-575054661598315749.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-575054661598315749.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-575054661598315749.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-575054661598315749.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-575054661598315749.gps.gpsil [style*="--pos:"]{position:var(--pos)}.gps-575054661598315749.gps.gpsil [style*="--right:"]{right:var(--right)}.gps-575054661598315749.gps.gpsil [style*="--rg:"]{row-gap:var(--rg)}.gps-575054661598315749.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-575054661598315749.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-575054661598315749.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-575054661598315749.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-575054661598315749.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-575054661598315749.gps.gpsil [style*="--z:"]{z-index:var(--z)}.gps-575054661598315749.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-575054661598315749.gps.gpsil [style*="--ai-tablet:"]{align-items:var(--ai-tablet)}.gps-575054661598315749.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-575054661598315749.gps.gpsil [style*="--bgc-tablet:"]{background-color:var(--bgc-tablet)}.gps-575054661598315749.gps.gpsil [style*="--bgi-tablet:"]{background-image:var(--bgi-tablet)}.gps-575054661598315749.gps.gpsil [style*="--bgp-tablet:"]{background-position:var(--bgp-tablet)}.gps-575054661598315749.gps.gpsil [style*="--bgr-tablet:"]{background-repeat:var(--bgr-tablet)}.gps-575054661598315749.gps.gpsil [style*="--bgs-tablet:"]{background-size:var(--bgs-tablet)}.gps-575054661598315749.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-575054661598315749.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-575054661598315749.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-575054661598315749.gps.gpsil [style*="--gtc-tablet:"]{grid-template-columns:var(--gtc-tablet)}.gps-575054661598315749.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-575054661598315749.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-575054661598315749.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-575054661598315749.gps.gpsil [style*="--mb-tablet:"]{margin-bottom:var(--mb-tablet)}.gps-575054661598315749.gps.gpsil [style*="--ml-tablet:"]{margin-left:var(--ml-tablet)}.gps-575054661598315749.gps.gpsil [style*="--mr-tablet:"]{margin-right:var(--mr-tablet)}.gps-575054661598315749.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-575054661598315749.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-575054661598315749.gps.gpsil [style*="--op-tablet:"]{opacity:var(--op-tablet)}.gps-575054661598315749.gps.gpsil [style*="--o-tablet:"]{order:var(--o-tablet)}.gps-575054661598315749.gps.gpsil [style*="--pc-tablet:"]{place-content:var(--pc-tablet)}.gps-575054661598315749.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-575054661598315749.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-575054661598315749.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-575054661598315749.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-575054661598315749.gps.gpsil [style*="--pos-tablet:"]{position:var(--pos-tablet)}.gps-575054661598315749.gps.gpsil [style*="--right-tablet:"]{right:var(--right-tablet)}.gps-575054661598315749.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-575054661598315749.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-575054661598315749.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-575054661598315749.gps.gpsil [style*="--ai-mobile:"]{align-items:var(--ai-mobile)}.gps-575054661598315749.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-575054661598315749.gps.gpsil [style*="--bgc-mobile:"]{background-color:var(--bgc-mobile)}.gps-575054661598315749.gps.gpsil [style*="--bgi-mobile:"]{background-image:var(--bgi-mobile)}.gps-575054661598315749.gps.gpsil [style*="--bgp-mobile:"]{background-position:var(--bgp-mobile)}.gps-575054661598315749.gps.gpsil [style*="--bgr-mobile:"]{background-repeat:var(--bgr-mobile)}.gps-575054661598315749.gps.gpsil [style*="--bgs-mobile:"]{background-size:var(--bgs-mobile)}.gps-575054661598315749.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-575054661598315749.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-575054661598315749.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-575054661598315749.gps.gpsil [style*="--gg-mobile:"]{grid-gap:var(--gg-mobile)}.gps-575054661598315749.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-575054661598315749.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-575054661598315749.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-575054661598315749.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-575054661598315749.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-575054661598315749.gps.gpsil [style*="--ml-mobile:"]{margin-left:var(--ml-mobile)}.gps-575054661598315749.gps.gpsil [style*="--mr-mobile:"]{margin-right:var(--mr-mobile)}.gps-575054661598315749.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-575054661598315749.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-575054661598315749.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-575054661598315749.gps.gpsil [style*="--op-mobile:"]{opacity:var(--op-mobile)}.gps-575054661598315749.gps.gpsil [style*="--o-mobile:"]{order:var(--o-mobile)}.gps-575054661598315749.gps.gpsil [style*="--pc-mobile:"]{place-content:var(--pc-mobile)}.gps-575054661598315749.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-575054661598315749.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-575054661598315749.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-575054661598315749.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-575054661598315749.gps.gpsil [style*="--pos-mobile:"]{position:var(--pos-mobile)}.gps-575054661598315749.gps.gpsil [style*="--right-mobile:"]{right:var(--right-mobile)}.gps-575054661598315749.gps.gpsil [style*="--rg-mobile:"]{row-gap:var(--rg-mobile)}.gps-575054661598315749.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-575054661598315749.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-575054661598315749.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-575054661598315749 .-gp-scale-100,.gps-575054661598315749 .-gp-translate-x-1\/2,.gps-575054661598315749 .-gp-translate-y-1\/2,.gps-575054661598315749 .before\:-gp-rotate-45:before,.gps-575054661598315749 .gp-rotate-0,.gps-575054661598315749 .gp-rotate-180,.gps-575054661598315749 .gp-translate-x-\[-50\%\],.gps-575054661598315749 .gp-translate-y-0,.gps-575054661598315749 .mobile\:gp-rotate-0,.gps-575054661598315749 .mobile\:gp-rotate-180,.gps-575054661598315749 .tablet\:gp-rotate-0,.gps-575054661598315749 .tablet\:gp-rotate-180{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-575054661598315749 .before\:gp-shadow-stock-counter:before,.gps-575054661598315749 .gp-shadow-md,.gps-575054661598315749 .gp-shadow-none{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-575054661598315749 .gp-g-subheading-2{font-family:var(--g-sh2-ff);font-size:var(--g-sh2-size);font-style:var(--g-sh2-fs);font-weight:var(--g-sh2-weight);letter-spacing:var(--g-sh2-ls);line-height:var(--g-sh2-lh)}.gps-575054661598315749 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-575054661598315749 .gp-g-paragraph-2{font-family:var(--g-p2-ff);font-size:var(--g-p2-size);font-style:var(--g-p2-fs);font-weight:var(--g-p2-weight);letter-spacing:var(--g-p2-ls);line-height:var(--g-p2-lh)}.gps-575054661598315749 .gp-g-s-medium{padding:8px 24px}.gps-575054661598315749 .gp-sr-only{clip:rect(0,0,0,0);border-width:0;height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;white-space:nowrap;width:1px}.gps-575054661598315749 .gp-pointer-events-none{pointer-events:none}.gps-575054661598315749 .gp-invisible{visibility:hidden}.gps-575054661598315749 .gp-static{position:static}.gps-575054661598315749 .\!gp-absolute{position:absolute!important}.gps-575054661598315749 .gp-absolute{position:absolute}.gps-575054661598315749 .gp-relative{position:relative}.gps-575054661598315749 .gp-inset-0{inset:0}.gps-575054661598315749 .gp-bottom-0{bottom:0}.gps-575054661598315749 .gp-bottom-\[-4px\]{bottom:-4px}.gps-575054661598315749 .gp-bottom-\[calc\(100\%\+20px\)\]{bottom:calc(100% + 20px)}.gps-575054661598315749 .gp-left-0{left:0}.gps-575054661598315749 .gp-left-1\/2,.gps-575054661598315749 .gp-left-\[50\%\]{left:50%}.gps-575054661598315749 .gp-right-0{right:0}.gps-575054661598315749 .gp-top-0{top:0}.gps-575054661598315749 .gp-top-1\/2{top:50%}.gps-575054661598315749 .gp-z-0{z-index:0}.gps-575054661598315749 .gp-z-1{z-index:1}.gps-575054661598315749 .gp-z-10{z-index:10}.gps-575054661598315749 .gp-z-2{z-index:2}.gps-575054661598315749 .gp-z-\[90\]{z-index:90}.gps-575054661598315749 .\!gp-m-0{margin:0!important}.gps-575054661598315749 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-575054661598315749 .gp-my-0{margin-bottom:0;margin-top:0}.gps-575054661598315749 .gp-my-\[1px\]{margin-bottom:1px;margin-top:1px}.gps-575054661598315749 .\!gp-ml-0{margin-left:0!important}.gps-575054661598315749 .gp-mb-0{margin-bottom:0}.gps-575054661598315749 .gp-mb-\[-10px\]{margin-bottom:-10px}.gps-575054661598315749 .gp-mr-1{margin-right:4px}.gps-575054661598315749 .gp-mt-2{margin-top:8px}.gps-575054661598315749 .gp-block{display:block}.gps-575054661598315749 .gp-inline-block{display:inline-block}.gps-575054661598315749 .\!gp-flex{display:flex!important}.gps-575054661598315749 .gp-flex{display:flex}.gps-575054661598315749 .gp-inline-flex{display:inline-flex}.gps-575054661598315749 .gp-grid{display:grid}.gps-575054661598315749 .\!gp-hidden{display:none!important}.gps-575054661598315749 .gp-hidden{display:none}.gps-575054661598315749 .gp-aspect-\[56\/32\]{aspect-ratio:56/32}.gps-575054661598315749 .gp-aspect-square{aspect-ratio:1/1}.gps-575054661598315749 .\!gp-h-fit{height:-moz-fit-content!important;height:fit-content!important}.gps-575054661598315749 .gp-h-0{height:0}.gps-575054661598315749 .gp-h-5{height:20px}.gps-575054661598315749 .gp-h-6{height:24px}.gps-575054661598315749 .gp-h-auto{height:auto}.gps-575054661598315749 .gp-h-full{height:100%}.gps-575054661598315749 .gp-max-h-full{max-height:100%}.gps-575054661598315749 .\!gp-min-h-full{min-height:100%!important}.gps-575054661598315749 .\!gp-w-full{width:100%!important}.gps-575054661598315749 .gp-w-14{width:56px}.gps-575054661598315749 .gp-w-5{width:20px}.gps-575054661598315749 .gp-w-6{width:24px}.gps-575054661598315749 .gp-w-\[12px\]{width:12px}.gps-575054661598315749 .gp-w-auto{width:auto}.gps-575054661598315749 .gp-w-full{width:100%}.gps-575054661598315749 .gp-w-max{width:-moz-max-content;width:max-content}.gps-575054661598315749 .\!gp-min-w-full{min-width:100%!important}.gps-575054661598315749 .gp-min-w-0{min-width:0}.gps-575054661598315749 .gp-min-w-\[45px\]{min-width:45px}.gps-575054661598315749 .gp-min-w-fit{min-width:-moz-fit-content;min-width:fit-content}.gps-575054661598315749 .gp-min-w-max{min-width:-moz-max-content;min-width:max-content}.gps-575054661598315749 .\!gp-max-w-\[150px\]{max-width:150px!important}.gps-575054661598315749 .\!gp-max-w-full{max-width:100%!important}.gps-575054661598315749 .\!gp-max-w-max{max-width:-moz-max-content!important;max-width:max-content!important}.gps-575054661598315749 .gp-max-w-full{max-width:100%}.gps-575054661598315749 .gp-flex-1{flex:1 1 0%}.gps-575054661598315749 .gp-shrink-0{flex-shrink:0}.gps-575054661598315749 .gp-shrink-\[99999\]{flex-shrink:99999}.gps-575054661598315749 .-gp-translate-x-1\/2{--tw-translate-x:-50%}.gps-575054661598315749 .-gp-translate-x-1\/2,.gps-575054661598315749 .-gp-translate-y-1\/2{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-575054661598315749 .-gp-translate-y-1\/2{--tw-translate-y:-50%}.gps-575054661598315749 .gp-translate-x-\[-50\%\]{--tw-translate-x:-50%}.gps-575054661598315749 .gp-translate-x-\[-50\%\],.gps-575054661598315749 .gp-translate-y-0{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-575054661598315749 .gp-translate-y-0{--tw-translate-y:0px}.gps-575054661598315749 .gp-rotate-0{--tw-rotate:0deg}.gps-575054661598315749 .gp-rotate-0,.gps-575054661598315749 .gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-575054661598315749 .gp-rotate-180{--tw-rotate:180deg}.gps-575054661598315749 .-gp-scale-100{--tw-scale-x:-1;--tw-scale-y:-1;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-575054661598315749 .\!gp-cursor-not-allowed{cursor:not-allowed!important}.gps-575054661598315749 .gp-cursor-default{cursor:default}.gps-575054661598315749 .gp-cursor-pointer{cursor:pointer}.gps-575054661598315749 .gp-select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.gps-575054661598315749 .gp-appearance-none{-webkit-appearance:none;-moz-appearance:none;appearance:none}.gps-575054661598315749 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-575054661598315749 .\!gp-flex-row{flex-direction:row!important}.gps-575054661598315749 .gp-flex-row{flex-direction:row}.gps-575054661598315749 .gp-flex-col{flex-direction:column}.gps-575054661598315749 .gp-flex-wrap{flex-wrap:wrap}.gps-575054661598315749 .\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-575054661598315749 .gp-items-start{align-items:flex-start}.gps-575054661598315749 .gp-items-center{align-items:center}.gps-575054661598315749 .gp-items-baseline{align-items:baseline}.gps-575054661598315749 .gp-justify-start{justify-content:flex-start}.gps-575054661598315749 .\!gp-justify-center{justify-content:center!important}.gps-575054661598315749 .gp-justify-center{justify-content:center}.gps-575054661598315749 .gp-justify-between{justify-content:space-between}.gps-575054661598315749 .gp-gap-2{gap:8px}.gps-575054661598315749 .gp-gap-\[6px\]{gap:6px}.gps-575054661598315749 .gp-gap-y-0{row-gap:0}.gps-575054661598315749 .gp-overflow-hidden{overflow:hidden}.gps-575054661598315749 .gp-truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.gps-575054661598315749 .gp-text-ellipsis{text-overflow:ellipsis}.gps-575054661598315749 .gp-whitespace-nowrap{white-space:nowrap}.gps-575054661598315749 .gp-break-words{overflow-wrap:break-word}.gps-575054661598315749 .\!gp-rounded-none{border-radius:0!important}.gps-575054661598315749 .gp-rounded{border-radius:4px}.gps-575054661598315749 .gp-rounded-\[8px\]{border-radius:8px}.gps-575054661598315749 .gp-rounded-full{border-radius:9999px}.gps-575054661598315749 .gp-rounded-md{border-radius:6px}.gps-575054661598315749 .gp-rounded-none{border-radius:0}.gps-575054661598315749 .\!gp-border-x-0{border-left-width:0!important;border-right-width:0!important}.gps-575054661598315749 .gp-border-y{border-bottom-width:1px;border-top-width:1px}.gps-575054661598315749 .gp-border-g-line-2{border-color:var(--g-c-line-2)}.gps-575054661598315749 .gp-bg-\[\#333333\]{--tw-bg-opacity:1;background-color:rgb(51 51 51/var(--tw-bg-opacity))}.gps-575054661598315749 .gp-bg-black\/50{background-color:rgba(0,0,0,.5)}.gps-575054661598315749 .gp-bg-black\/80{background-color:rgba(0,0,0,.8)}.gps-575054661598315749 .gp-bg-g-bg-2{background-color:var(--g-c-bg-2)}.gps-575054661598315749 .gp-bg-g-bg-3{background-color:var(--g-c-bg-3)}.gps-575054661598315749 .gp-bg-g-brand{background-color:var(--g-c-brand)}.gps-575054661598315749 .gp-bg-g-text-1{background-color:var(--g-c-text-1)}.gps-575054661598315749 .gp-bg-transparent{background-color:transparent}.gps-575054661598315749 .\!gp-bg-\[length\:200\%\]{background-size:200%!important}.gps-575054661598315749 .gp-bg-\[length\:60px_100\%\]{background-size:60px 100%}.gps-575054661598315749 .gp-bg-auto{background-size:auto}.gps-575054661598315749 .gp-object-cover{-o-object-fit:cover;object-fit:cover}.gps-575054661598315749 .gp-p-4{padding:16px}.gps-575054661598315749 .gp-p-\[4px\]{padding:4px}.gps-575054661598315749 .gp-px-0{padding-left:0;padding-right:0}.gps-575054661598315749 .gp-px-1{padding-left:4px;padding-right:4px}.gps-575054661598315749 .gp-px-2{padding-left:8px;padding-right:8px}.gps-575054661598315749 .gp-px-4{padding-left:16px;padding-right:16px}.gps-575054661598315749 .gp-px-\[8px\]{padding-left:8px;padding-right:8px}.gps-575054661598315749 .gp-py-0{padding-bottom:0;padding-top:0}.gps-575054661598315749 .gp-py-1{padding-bottom:4px;padding-top:4px}.gps-575054661598315749 .gp-py-2{padding-bottom:8px;padding-top:8px}.gps-575054661598315749 .gp-py-\[4px\]{padding-bottom:4px;padding-top:4px}.gps-575054661598315749 .\!gp-pb-0{padding-bottom:0!important}.gps-575054661598315749 .gp-pb-0{padding-bottom:0}.gps-575054661598315749 .gp-pb-1{padding-bottom:4px}.gps-575054661598315749 .gp-pl-4{padding-left:16px}.gps-575054661598315749 .gp-pr-1{padding-right:4px}.gps-575054661598315749 .gp-pr-6{padding-right:24px}.gps-575054661598315749 .gp-text-left{text-align:left}.gps-575054661598315749 .gp-text-center{text-align:center}.gps-575054661598315749 .gp-text-\[12px\]{font-size:12px}.gps-575054661598315749 .gp-leading-\[0\]{line-height:0}.gps-575054661598315749 .gp-text-\[\#F9F9F9\]{--tw-text-opacity:1;color:rgb(249 249 249/var(--tw-text-opacity))}.gps-575054661598315749 .gp-text-g-text-1{color:var(--g-c-text-1)}.gps-575054661598315749 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-575054661598315749 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-575054661598315749 .gp-text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity))}.gps-575054661598315749 .gp-text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity))}.gps-575054661598315749 .gp-no-underline{text-decoration-line:none}.gps-575054661598315749 .gp-decoration-g-text-1{text-decoration-color:var(--g-c-text-1)}.gps-575054661598315749 .gp-opacity-0{opacity:0}.gps-575054661598315749 .gp-opacity-20{opacity:.2}.gps-575054661598315749 .gp-opacity-25{opacity:.25}.gps-575054661598315749 .gp-opacity-30{opacity:.3}.gps-575054661598315749 .gp-opacity-75{opacity:.75}.gps-575054661598315749 .gp-shadow-md{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color)}.gps-575054661598315749 .gp-shadow-md,.gps-575054661598315749 .gp-shadow-none{box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-575054661598315749 .gp-shadow-none{--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-575054661598315749 .gp-outline-none{outline:2px solid transparent;outline-offset:2px}.gps-575054661598315749 .gp-outline-1{outline-width:1px}.gps-575054661598315749 .-gp-outline-offset-1{outline-offset:-1px}.gps-575054661598315749 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-575054661598315749 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-575054661598315749 .gp-transition-opacity{transition-duration:.15s;transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-575054661598315749 .gp-duration-100{transition-duration:.1s}.gps-575054661598315749 .gp-duration-150{transition-duration:.15s}.gps-575054661598315749 .gp-duration-200{transition-duration:.2s}.gps-575054661598315749 .gp-duration-300{transition-duration:.3s}.gps-575054661598315749 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-575054661598315749 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-575054661598315749 .before\:gp-absolute:before{content:var(--tw-content);position:absolute}.gps-575054661598315749 .before\:gp-left-0:before{content:var(--tw-content);left:0}.gps-575054661598315749 .before\:gp-top-\[50\%\]:before{content:var(--tw-content);top:50%}.gps-575054661598315749 .before\:gp-z-1:before{content:var(--tw-content);z-index:1}.gps-575054661598315749 .before\:gp-hidden:before{content:var(--tw-content);display:none}.gps-575054661598315749 .before\:gp-h-full:before{content:var(--tw-content);height:100%}.gps-575054661598315749 .before\:gp-w-full:before{content:var(--tw-content);width:100%}.gps-575054661598315749 .before\:-gp-rotate-45:before{--tw-rotate:-45deg;content:var(--tw-content);transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}@keyframes gp-stockCounterShift{to{background-position:60px 100%;content:var(--tw-content)}}.gps-575054661598315749 .before\:gp-animate-shift:before{animation:gp-stockCounterShift 2s linear infinite;content:var(--tw-content)}.gps-575054661598315749 .before\:gp-rounded-md:before{border-radius:6px;content:var(--tw-content)}.gps-575054661598315749 .before\:gp-border-t:before{border-top-width:1px;content:var(--tw-content)}.gps-575054661598315749 .before\:gp-bg-stock-counter-animate:before{background-image:linear-gradient(45deg,transparent 25%,hsla(0,0%,93%,.5) 0,hsla(0,0%,93%,.5) 30%,transparent 0,transparent 35%,hsla(0,0%,93%,.5) 0,hsla(0,0%,93%,.5) 70%,transparent 0);content:var(--tw-content)}.gps-575054661598315749 .before\:gp-bg-\[length\:60px_100\%\]:before{background-size:60px 100%;content:var(--tw-content)}.gps-575054661598315749 .before\:gp-shadow-stock-counter:before{--tw-shadow:inset 0 0px 1px rgba(0,0,0,.2),inset 0 -2px 1px rgba(0,0,0,.2);--tw-shadow-colored:inset 0 0px 1px var(--tw-shadow-color),inset 0 -2px 1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow);content:var(--tw-content)}.gps-575054661598315749 .before\:gp-content-\[\'\'\]:before{--tw-content:"";content:var(--tw-content)}.gps-575054661598315749 .after\:gp-absolute:after{content:var(--tw-content);position:absolute}.gps-575054661598315749 .after\:gp-bottom-\[-10px\]:after{bottom:-10px;content:var(--tw-content)}.gps-575054661598315749 .after\:gp-left-0:after{content:var(--tw-content);left:0}.gps-575054661598315749 .after\:gp-w-full:after{content:var(--tw-content);width:100%}.gps-575054661598315749 .after\:gp-p-\[7px\]:after{content:var(--tw-content);padding:7px}.gps-575054661598315749 .after\:gp-content-\[\'\'\]:after{--tw-content:"";content:var(--tw-content)}@media (hover:hover) and (pointer:fine){.gps-575054661598315749 .hover\:gp-border-g-line-3:hover{border-color:var(--g-c-line-3)}.gps-575054661598315749 .hover\:gp-bg-\[\#ef0800\]:hover{--tw-bg-opacity:1;background-color:rgb(239 8 0/var(--tw-bg-opacity))}.gps-575054661598315749 .hover\:gp-bg-g-bg-3:hover{background-color:var(--g-c-bg-3)}.gps-575054661598315749 .hover\:gp-text-black:hover{--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity))}.gps-575054661598315749 .hover\:gp-text-g-text-2:hover{color:var(--g-c-text-2)}}.gps-575054661598315749 .active\:gp-bg-g-bg-3:active{background-color:var(--g-c-bg-3)}.gps-575054661598315749 .active\:gp-text-g-text-2:active{color:var(--g-c-text-2)}.gps-575054661598315749 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-575054661598315749 .disabled\:gp-cursor-not-allowed:disabled{cursor:not-allowed}.gps-575054661598315749 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-575054661598315749 .gp-group:hover .group-hover\:gp-visible{visibility:visible}.gps-575054661598315749 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}.gps-575054661598315749 .gp-group:hover .group-hover\:gp-opacity-100{opacity:1}}.gps-575054661598315749 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-575054661598315749 .data-\[disabled\=\'disabled\'\]\:gp-pointer-events-none[data-disabled=disabled]{pointer-events:none}.gps-575054661598315749 .data-\[visible\=true\]\:gp-block[data-visible=true]{display:block}.gps-575054661598315749 .data-\[hidden\=\'false\'\]\:gp-flex[data-hidden=false]{display:flex}.gps-575054661598315749 .data-\[hidden\=true\]\:gp-hidden[data-hidden=true],.gps-575054661598315749 .data-\[only-image\=true\]\:gp-hidden[data-only-image=true],.gps-575054661598315749 .data-\[visible\=false\]\:gp-hidden[data-visible=false]{display:none}.gps-575054661598315749 .data-\[disabled\=\'disabled\'\]\:\!gp-cursor-not-allowed[data-disabled=disabled]{cursor:not-allowed!important}.gps-575054661598315749 .data-\[disabled\=true\]\:gp-opacity-60[data-disabled=true]{opacity:.6}.gps-575054661598315749 .data-\[outline\=active\]\:gp-outline[data-outline=active]{outline-style:solid}.gps-575054661598315749 .data-\[outline\=deactive\]\:after\:\!gp-border-transparent[data-outline=deactive]:after{border-color:transparent!important;content:var(--tw-content)}.gps-575054661598315749 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-visible{visibility:visible}.gps-575054661598315749 .gp-group[data-state=loading] .group-data-\[state\=loading\]\:gp-invisible,.gps-575054661598315749 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@keyframes gp-spin{to{transform:rotate(1turn)}}.gps-575054661598315749 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-animate-spin{animation:gp-spin 1s linear infinite}.gps-575054661598315749 .scrollbar-thumb\:gp-rounded-2xl::-webkit-scrollbar-thumb,.gps-575054661598315749 .scrollbar-track\:gp-rounded-2xl::-webkit-scrollbar-track{border-radius:16px}.gps-575054661598315749 .scrollbar-thumb\:gp-bg-gray-400::-webkit-scrollbar-thumb{--tw-bg-opacity:1;background-color:rgb(156 163 175/var(--tw-bg-opacity))}@media (max-width:1024px){.gps-575054661598315749 .tablet\:gp-static{position:static}.gps-575054661598315749 .tablet\:\!gp-absolute{position:absolute!important}.gps-575054661598315749 .tablet\:gp-relative{position:relative}.gps-575054661598315749 .tablet\:gp-left-0{left:0}.gps-575054661598315749 .tablet\:gp-right-0{right:0}.gps-575054661598315749 .tablet\:gp-z-2{z-index:2}.gps-575054661598315749 .tablet\:gp-block{display:block}.gps-575054661598315749 .tablet\:\!gp-flex{display:flex!important}.gps-575054661598315749 .tablet\:\!gp-hidden{display:none!important}.gps-575054661598315749 .tablet\:gp-hidden{display:none}.gps-575054661598315749 .tablet\:\!gp-min-h-full{min-height:100%!important}.gps-575054661598315749 .tablet\:gp-rotate-0{--tw-rotate:0deg}.gps-575054661598315749 .tablet\:gp-rotate-0,.gps-575054661598315749 .tablet\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-575054661598315749 .tablet\:gp-rotate-180{--tw-rotate:180deg}.gps-575054661598315749 .tablet\:\!gp-flex-row{flex-direction:row!important}.gps-575054661598315749 .tablet\:gp-flex-row{flex-direction:row}.gps-575054661598315749 .tablet\:gp-flex-col{flex-direction:column}.gps-575054661598315749 .tablet\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-575054661598315749 .tablet\:\!gp-content-stretch{align-content:stretch!important}.gps-575054661598315749 .tablet\:\!gp-justify-center{justify-content:center!important}.gps-575054661598315749 .tablet\:gp-px-0{padding-left:0;padding-right:0}.gps-575054661598315749 .tablet\:gp-py-0{padding-bottom:0;padding-top:0}}@media (max-width:767px){.gps-575054661598315749 .mobile\:gp-static{position:static}.gps-575054661598315749 .mobile\:\!gp-absolute{position:absolute!important}.gps-575054661598315749 .mobile\:gp-relative{position:relative}.gps-575054661598315749 .mobile\:gp-left-0{left:0}.gps-575054661598315749 .mobile\:gp-right-0{right:0}.gps-575054661598315749 .mobile\:gp-z-2{z-index:2}.gps-575054661598315749 .mobile\:gp-block{display:block}.gps-575054661598315749 .mobile\:\!gp-flex{display:flex!important}.gps-575054661598315749 .mobile\:\!gp-hidden{display:none!important}.gps-575054661598315749 .mobile\:gp-hidden{display:none}.gps-575054661598315749 .mobile\:\!gp-min-h-full{min-height:100%!important}.gps-575054661598315749 .mobile\:\!gp-max-w-\[0px\]{max-width:0!important}.gps-575054661598315749 .mobile\:gp-rotate-0{--tw-rotate:0deg}.gps-575054661598315749 .mobile\:gp-rotate-0,.gps-575054661598315749 .mobile\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-575054661598315749 .mobile\:gp-rotate-180{--tw-rotate:180deg}.gps-575054661598315749 .mobile\:\!gp-flex-row{flex-direction:row!important}.gps-575054661598315749 .mobile\:gp-flex-row{flex-direction:row}.gps-575054661598315749 .mobile\:gp-flex-col{flex-direction:column}.gps-575054661598315749 .mobile\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-575054661598315749 .mobile\:\!gp-content-stretch{align-content:stretch!important}.gps-575054661598315749 .mobile\:\!gp-justify-center{justify-content:center!important}.gps-575054661598315749 .mobile\:gp-overflow-hidden{overflow:hidden}.gps-575054661598315749 .mobile\:gp-px-0,.gps-575054661598315749 .mobile\:gp-px-\[0px\]{padding-left:0;padding-right:0}.gps-575054661598315749 .mobile\:gp-py-0{padding-bottom:0;padding-top:0}.gps-575054661598315749 .mobile\:after\:gp-p-\[0px\]:after{content:var(--tw-content);padding:0}}@media (max-width:1024px){.gps-575054661598315749 .tablet\:\[\&\>\*\]\:\!gp-justify-center>*{justify-content:center!important}}@media (max-width:767px){.gps-575054661598315749 .mobile\:\[\&\>\*\]\:\!gp-justify-center>*{justify-content:center!important}}.gps-575054661598315749 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-575054661598315749 .\[\&\>svg\]\:\!gp-h-\[var\(--size-desktop\)\]>svg{height:var(--size-desktop)!important}.gps-575054661598315749 .\[\&\>svg\]\:gp-h-full>svg{height:100%}.gps-575054661598315749 .\[\&\>svg\]\:\!gp-w-\[var\(--size-desktop\)\]>svg{width:var(--size-desktop)!important}.gps-575054661598315749 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}.gps-575054661598315749 .\[\&\>svg\]\:gp-w-full>svg{width:100%}@media (max-width:1024px){.gps-575054661598315749 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}.gps-575054661598315749 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--size-tablet\)\]>svg{height:var(--size-tablet)!important}.gps-575054661598315749 .tablet\:\[\&\>svg\]\:\!gp-w-\[var\(--size-tablet\)\]>svg{width:var(--size-tablet)!important}}@media (max-width:767px){.gps-575054661598315749 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}.gps-575054661598315749 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--size-mobile\)\]>svg{height:var(--size-mobile)!important}.gps-575054661598315749 .mobile\:\[\&\>svg\]\:\!gp-w-\[var\(--size-mobile\)\]>svg{width:var(--size-mobile)!important}}.gps-575054661598315749 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-575054661598315749 .\[\&_button\]\:data-\[active\=true\]\:\!gp-bg-\[\#00A042\][data-active=true] button{--tw-bg-opacity:1!important;background-color:rgb(0 160 66/var(--tw-bg-opacity))!important}.gps-575054661598315749 .\[\&_p\]\:gp-inline p{display:inline}.gps-575054661598315749 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-575054661598315749 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-575054661598315749 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gzFZd7k4lx" data-id="gzFZd7k4lx"
        style="--blockPadding:base;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--op-mobile:100%;--op-tablet:100%;--pt:80px;--pl:15px;--pb:48px;--pr:15px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:14px;--pr-mobile:0px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#ffffff;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gzFZd7k4lx gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gPEVOsRNae gp-relative gp-flex gp-flex-col"
    >
      
    {%- assign gpBkProduct = product -%}
    
        {%- liquid
          if request.page_type != 'product'
            assign product = all_products['ideaformer-ir3-v2-conveyor-belt-3d-printer']
            assign productId = '8910822244605' | times: 1
            if product == empty or product == null
              paginate collections.all.products by 100000
                for item in collections.all.products
                  if item.id == productId
                    assign product = item
                  endif
                endfor
              endpaginate
            endif
          endif
        -%}
        

    {%-if product != empty and product != null -%}
      {%- assign initVariantId =  -%}
      {%- assign product_form_id = 'product-form-' | append: "gmLgRHMcSG" -%}
      {%- assign variant = product.selected_or_first_available_variant -%}
      {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%-if productSelectedVariant == empty or productSelectedVariant == null -%}
        {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      {%-if variant == empty or variant == null -%}
        {%- assign variant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      <gp-product data-uid="gmLgRHMcSG" data-id="gmLgRHMcSG"   gp-context='{"productId": {{ product.id }}, "preSelectedOptionIds": [], "isSyncProduct": "true", "variantSelected": {{ variant | json | escape }}, "inventoryQuantity": {{ variant.inventory_quantity }}, "quantity": 1, "formId": "{{ product_form_id }}" }'
        gp-data='{"variantSelected": {{ variant | json | escape }}, "quantity": 1, "productUrl":{{ product.url | json | escape }}, "productHandle":{{ product.handle | json | escape }}, "collectionUrl": {{ collection.url | json | escape }}, "collectionHandle": {{ collection.handle | json | escape }}}'>
        <product-form class="product-form">
          {%- form 'product', product, id: product_form_id, class: 'form contents', data-type: 'add-to-cart-form', autocomplete: 'off'  -%}
            <input type="hidden" name="id" value="{{ variant.id }}" />
            <input type="hidden" name="quantity" value="{{ quantity }}" />
            <button type="submit" onclick="return false;" style="display:none;"></button>
            
       
      
    <div
      id="gmLgRHMcSG" data-id="gmLgRHMcSG-row"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--op-mobile:100%;--mb-mobile:14px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%"
        class="gmLgRHMcSG gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    {% render 'gp-section-575054661598315749-0', product: product, variant: variant, product_form_id: product_form_id, productSelectedVariant: productSelectedVariant, form: form %}<div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gD8AGtTjap gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gEOcXSzW7U" data-id="gEOcXSzW7U"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--op-mobile:100%;--pl:32px;--mb-mobile:var(--g-s-l);--pl-mobile:15px;--pr-mobile:15px;--cg:8px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gEOcXSzW7U gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g0Fvw1K0TD gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:8px" class="giv4c7lhPo ">
      
    {%- unless product -%}
      <p>{{ "gempages.ProductTitle.product_not_found" | t }}</p>
    {%- else -%}
      
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="giv4c7lhPo">
    <div
      
        class="giv4c7lhPo "
        
      >
      <div  >
        <h1
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-product-title gp-text-g-text-2"
          style="--w:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:var(--g-c-text-2, text-2);--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:33px;--size-tablet:33px;--size-mobile:29px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ product.title }}</h1>
      </div>
    </div>
    </gp-text>
    
      
    {%- endunless -%}
  
      </div>
       
      
    <div
      parentTag="Col" id="g3lzBq7jXu" data-id="g3lzBq7jXu"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:16px;--cg:24px;--cg-mobile:21px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g3lzBq7jXu gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gLzY_RjfnI gp-relative gp-flex gp-flex-col"
    >
      
      <gp-product-price
        data-id="gfWu3-mmw_"
        class="gfWu3-mmw_ gp-product-price group-data-[state=loading]:gp-invisible data-[hidden=true]:gp-hidden"
        gp-data='{"priceType":"regular","uid":"gfWu3-mmw_","locale":"{{shop.locale}}","currency":"{{shop.currency}}","moneyFormat":"{{ shop.money_format | replace: '"', '\\"' | escape }}"}'
        
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
        data-hidden="false"
      >
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-price-gfWu3-mmw_"
        class=" "
        
      >
      <div  >
        <div
          type="regular"
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-price gp-money gp-decoration-g-text-1"
          style="--w:100%;--tdc:text-1;--tdt:1;--ta:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >
     {% if variant.price %} 
       {{ variant.price | money}}
     {% endif %}
   </div>
      </div>
    </div>
    </gp-text>
    
      </gp-product-price>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-product-price.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gFNmJzK8ZI gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gpj3nXzF_r" data-id="gpj3nXzF_r"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--op-tablet:100%;--cg:12px;--cg-mobile:7px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gpj3nXzF_r gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="g2maB48QrE gp-relative gp-flex gp-flex-col"
    >
      
    
    <div
    data-id="gyvXxSnpNu"
      
      data-id="gyvXxSnpNu"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">

    <div
            class="gp-flex gp-flex-wrap"
            style="--cg:2px;--jc:left">
            <div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gDlbegGJqm ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gYHq3cWbbZ"
    >
      <div 
      data-id="gYHq3cWbbZ"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:16px;--w-tablet:16px;--w-mobile:12px;--h:16px;--h-tablet:16px;--h-mobile:12px;--minw:16px;--minw-tablet:16px;--minw-mobile:12px;--height-desktop:16px;--height-tablet:16px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor"><path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="g3CxRI4n6q ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gM4cet7IA-"
    >
      <div 
      data-id="gM4cet7IA-"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:16px;--w-tablet:16px;--w-mobile:12px;--h:16px;--h-tablet:16px;--h-mobile:12px;--minw:16px;--minw-tablet:16px;--minw-mobile:12px;--height-desktop:16px;--height-tablet:16px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor"><path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gTKWqhnIu8 ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gVHIySKEZ2"
    >
      <div 
      data-id="gVHIySKEZ2"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:16px;--w-tablet:16px;--w-mobile:12px;--h:16px;--h-tablet:16px;--h-mobile:12px;--minw:16px;--minw-tablet:16px;--minw-mobile:12px;--height-desktop:16px;--height-tablet:16px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor"><path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="g1JHhOkjlA ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gZgRN4SN4j"
    >
      <div 
      data-id="gZgRN4SN4j"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:16px;--w-tablet:16px;--w-mobile:12px;--h:16px;--h-tablet:16px;--h-mobile:12px;--minw:16px;--minw-tablet:16px;--minw-mobile:12px;--height-desktop:16px;--height-tablet:16px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor"><path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gM-eI7JQtm ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gqF8J3Xo0m"
    >
      <div 
      data-id="gqF8J3Xo0m"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:16px;--w-tablet:16px;--w-mobile:12px;--h:16px;--h-tablet:16px;--h-mobile:12px;--minw:16px;--minw-tablet:16px;--minw-mobile:12px;--height-desktop:16px;--height-tablet:16px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor"><path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div>
         </div>
    </div>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="g4qYFPPpJk gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g7VwU_pVAI">
    <div
      parentTag="Col"
        class="g7VwU_pVAI "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg7VwU_pVAI_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-g7VwU_pVAI">
        .g7VwU_pVAI {

}
.g7VwU_pVAI p {

}
      </style>
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    <div class="gp-flex gp-w-full !gp-justify-center tablet:!gp-justify-center mobile:!gp-justify-center" style="--h:auto;--h-tablet:fit-content;--h-mobile:fit-content;--d:flex;--d-mobile:none;--d-tablet:flex;--mb:16px">
      <gp-hero-banner
        gp-data='{"target":"_blank","background":{"desktop":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0762/6113/0493/files/gempages_572751041980793671-718fac57-412a-43c8-9709-2bd4ff5c0ad3.jpg","width":882,"height":344,"backupFileKey":"gempages_572751041980793671-718fac57-412a-43c8-9709-2bd4ff5c0ad3.jpg","storage":"FILE_CONTENT"},"lazyLoad":false,"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"},"tablet":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0762/6113/0493/files/gempages_572751041980793671-718fac57-412a-43c8-9709-2bd4ff5c0ad3.jpg","width":882,"height":344,"backupFileKey":"gempages_572751041980793671-718fac57-412a-43c8-9709-2bd4ff5c0ad3.jpg","storage":"FILE_CONTENT"},"lazyLoad":false,"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"},"mobile":{"attachment":"scroll","color":"bg-2","image":{"src":"https://ucarecdn.com/46d5a88e-90ac-48f6-9cb5-b666cd138e46/-/format/auto/"},"lazyLoad":false,"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"}},"uid":"g1TLqjF3rQ","enableParallax":false,"speedParallax":0.6,"hoverEffect":false,"hoverEffectScale":"110%","layout":{"desktop":{"cols":[12],"display":"fill"}},"contentPosition1Col":{"desktop":"center"},"contentPosition2Col":{"desktop":"center"}}'
        gp-href=""
        
        class="g1TLqjF3rQ gp-group/hero gp-flex gp-flex-col gp-transition-colors gp-duration-200 gp-ease-in-out gp-w-full"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:flex;--d-mobile:none;--d-tablet:flex;--op:100%;--op-mobile:100%;--h:auto;--h-tablet:fit-content;--h-mobile:fit-content;--w:100%;--w-tablet:100%;--w-mobile:100%"
        data-id="g1TLqjF3rQ"
      >
        
      <div
        class="gp-relative gp-flex gp-flex-col gp-items-center gp-overflow-hidden gp_haslazybg !gp-justify-center tablet:!gp-justify-center mobile:!gp-justify-center"
        style="--h:auto;--h-tablet:fit-content;--h-mobile:fit-content;--bs:solid;--bw:0px 0px 0px 0px;--bc:#000000;--bblr:6px;--bbrr:6px;--btlr:6px;--btrr:6px;--bgc:var(--g-c-bg-2);--bgc-tablet:var(--g-c-bg-2);--bgc-mobile:var(--g-c-bg-2);--shadow:none"
      >
        
  <picture style="border-radius: inherit" class="gp-w-full">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/46d5a88e-90ac-48f6-9cb5-b666cd138e46/-/format/auto/" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODgyIiBoZWlnaHQ9IjM0NCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctODgyLTM0NCI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iODgyIiBoZWlnaHQ9IjM0NCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iODgyIiBoZWlnaHQ9IjM0NCIgZmlsbD0idXJsKCNnLTg4Mi0zNDQpIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii04ODIiIHRvPSI4ODIiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-718fac57-412a-43c8-9709-2bd4ff5c0ad3.jpg" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODgyIiBoZWlnaHQ9IjM0NCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctODgyLTM0NCI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iODgyIiBoZWlnaHQ9IjM0NCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iODgyIiBoZWlnaHQ9IjM0NCIgZmlsbD0idXJsKCNnLTg4Mi0zNDQpIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii04ODIiIHRvPSI4ODIiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        title
        class="adaptive-hero-banner gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODgyIiBoZWlnaHQ9IjM0NCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctODgyLTM0NCI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iODgyIiBoZWlnaHQ9IjM0NCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iODgyIiBoZWlnaHQ9IjM0NCIgZmlsbD0idXJsKCNnLTg4Mi0zNDQpIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii04ODIiIHRvPSI4ODIiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="{{ "gempages_572751041980793671-718fac57-412a-43c8-9709-2bd4ff5c0ad3.jpg" | file_url }}"
        width="100%"
        alt=""
        style="--d:none;--d-tablet:none;--d-mobile:none;--h:auto;--h-tablet:fit-content;--h-mobile:fit-content;--w:100%;--w-tablet:100%;--w-mobile:100%;--op:0;--z:-1"
      />
    
  </picture>
        <div class="gp-absolute gp-w-full gp-h-full">
          <div
            class="gp-overflow-hidden gp-relative gp-w-full gp-h-full"
            >
            <div
              aria-label="Background Image"
              role="banner"
              class='gp-hero-banner-bg gp-absolute gp-overflow-hidden gp-w-full gp-h-full top-0 left-0'
              style="clip-path:inset(0 0 0 round 6px 6px 6px 6px)"
            >
            <div class="hero-banner-bg-parallax gp-hero-banner-image-background gp_lazybg"
              style="--bgi:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODgyIiBoZWlnaHQ9IjM0NCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctODgyLTM0NCI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iODgyIiBoZWlnaHQ9IjM0NCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iODgyIiBoZWlnaHQ9IjM0NCIgZmlsbD0idXJsKCNnLTg4Mi0zNDQpIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii04ODIiIHRvPSI4ODIiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=);--bgi-tablet:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODgyIiBoZWlnaHQ9IjM0NCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctODgyLTM0NCI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iODgyIiBoZWlnaHQ9IjM0NCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iODgyIiBoZWlnaHQ9IjM0NCIgZmlsbD0idXJsKCNnLTg4Mi0zNDQpIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii04ODIiIHRvPSI4ODIiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=);--bgi-mobile:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODgyIiBoZWlnaHQ9IjM0NCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctODgyLTM0NCI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iODgyIiBoZWlnaHQ9IjM0NCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iODgyIiBoZWlnaHQ9IjM0NCIgZmlsbD0idXJsKCNnLTg4Mi0zNDQpIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii04ODIiIHRvPSI4ODIiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=);--bgc:var(--g-c-bg-2);--bgc-tablet:var(--g-c-bg-2);--bgc-mobile:var(--g-c-bg-2);--bgp:50% 50%;--bgp-tablet:50% 50%;--bgp-mobile:50% 50%;--bgs:cover;--bgs-tablet:cover;--bgs-mobile:cover;--bgr:no-repeat;--bgr-tablet:no-repeat;--bgr-mobile:no-repeat;--duration:0.5s;--scale:110%;--pos:absolute;--pos-tablet:absolute;--pos-mobile:absolute;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--top:;--top-tablet:;--top-mobile:"
            >
            
            
  <img
      
      
      draggable="false"
      class="gp-absolute gp-top-0 gp-invisible gp-w-full gp_lazyload gp-h-full gp_lazyforbg   gp_lazyload"
      data-src="{{ "gempages_572751041980793671-718fac57-412a-43c8-9709-2bd4ff5c0ad3.jpg" | file_url }}" data-srcset="{{ "gempages_572751041980793671-718fac57-412a-43c8-9709-2bd4ff5c0ad3.jpg" | file_url }}" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" width="2237" height="1678" alt="lazy image desktop"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      
    />
  
  <img
      
      
      draggable="false"
      class="gp-absolute gp-top-0 gp-invisible gp-w-full gp_lazyload gp-h-full gp_lazyforbg gp_lazybg_tl  gp_lazyload"
      data-src="{{ "gempages_572751041980793671-718fac57-412a-43c8-9709-2bd4ff5c0ad3.jpg" | file_url }}" data-srcset="{{ "gempages_572751041980793671-718fac57-412a-43c8-9709-2bd4ff5c0ad3.jpg" | file_url }}" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" width="2237" height="1678" alt="lazy image tablet"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      
    />
  
  <img
      
      
      draggable="false"
      class="gp-absolute gp-top-0 gp-invisible gp-w-full gp_lazyload gp-h-full gp_lazyforbg  gp_lazybg_mb gp_lazyload"
      data-src="https://ucarecdn.com/46d5a88e-90ac-48f6-9cb5-b666cd138e46/-/format/auto/" data-srcset="https://ucarecdn.com/46d5a88e-90ac-48f6-9cb5-b666cd138e46/-/format/auto/" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" width="2237" height="1678" alt="lazy image mobile"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      
    />
  
            </div></div>
          </div>
          
          </div>
          
       
      
    <div
      id data-id
        style="--pl:22px;--pr:22px;--pt:13px;--pb:50px;--pl-tablet:22px;--pr-tablet:22px;--pt-tablet:13px;--pb-tablet:50px;--cg:32px;--pc:center;--pc-tablet:center;--pc-mobile:center;--gtc:minmax(0, 12fr);--w:1200px;--w-tablet:var(--g-ct-w, 1200px);--w-mobile:var(--g-ct-w, 1200px)"
        class="gp-hero-banner-row gp-z-1 gp-w-full tablet:gp-relative mobile:gp-relative tablet:[&>*>*]:!gp-justify-undefined mobile:[&>*>*]:!gp-justify-undefined tablet:!gp-content-stretch tablet:[&>*]:!gp-justify-center mobile:!gp-content-stretch mobile:[&>*]:!gp-justify-center gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="g0vpKVfgfS gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gW4qkFIgkt">
    <div
      parentTag="Col"
        class="gW4qkFIgkt "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:28px;--size-tablet:28px;--size-mobile:24px;--lh:110%;--lh-tablet:110%;--lh-mobile:110%;--c:#ffffff;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggW4qkFIgkt_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gW4qkFIgkt">
        .gW4qkFIgkt {

}
.gW4qkFIgkt p {

}
      </style>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gpeoYAYmGJ">
    <div
      parentTag="Col"
        class="gpeoYAYmGJ "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:48px;--size-tablet:48px;--size-mobile:42px;--lh:110%;--lh-tablet:110%;--lh-mobile:110%;--c:#A8F5FF;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggpeoYAYmGJ_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gpeoYAYmGJ">
        .gpeoYAYmGJ {

}
.gpeoYAYmGJ p {

}
      </style>
    
       
      
    <div
      parentTag="Col" id="gEi9a5DozG" data-id="gEi9a5DozG"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gEi9a5DozG gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gM-n0ONxsH gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper  class="ghsqPAOP8G ">
      
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <gp-icon-list
      uid="ghsqPAOP8G"
      data-id="ghsqPAOP8G"
      gp-data='{"position":{"desktop":"center","mobile":"center","tablet":"center"},"iconWidth":{"desktop":16,"mobile":16,"tablet":16}}'
       style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">
     
      <div class="gp-flex" style="--jc:left">
        <div
        class="gp-inline-flex gp-flex-col "
        style="--rg:16px">
        
           <div
            class="gp-icon-list-wrapper gp-flex gp-child-item-ghsqPAOP8G"
            style="--cg:0px;--ai:center;--ai-tablet:center;--ai-mobile:center"
            data-index="0"
          >
            <div class="gp-icon-list-icon gp-inline-flex gp-shrink-0 gp-overflow-hidden" style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:1px 1px 1px 1px;--bc:#000000;--bg:transparent;--pos:relative;--pos-tablet:relative;--pos-mobile:relative;--top:inherit;--top-tablet:inherit;--top-mobile:inherit">
              <span
                class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--size-desktop)] tablet:[&>svg]:!gp-h-[var(--size-tablet)] mobile:[&>svg]:!gp-h-[var(--size-mobile)] [&>svg]:!gp-w-[var(--size-desktop)] tablet:[&>svg]:!gp-w-[var(--size-tablet)] mobile:[&>svg]:!gp-w-[var(--size-mobile)]"
                style="--c:#57B8FF;--w:100%;--h:100%;--size-desktop:16px;--size-tablet:16px;--size-mobile:16px"
              >
               <svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817731477569896">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M156,128a28,28,0,1,1-28-28A28,28,0,0,1,156,128Z" /></svg>
              </span>
            </div>
             <div class=" gp-text-left gp-icon-list-text" style="--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:8px;--size-tablet:8px;--size-mobile:8px;--c:#ffffff">
                {{ section.settings.gghsqPAOP8G_childItem_0 | replace: '$locationOrigin', locationOrigin }}
            </div>
            </div>
      </div>
      </div>
    </gp-icon-list>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-icon-list.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="guuthzV54S gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper  class="gIegJobIXU ">
      
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <gp-icon-list
      uid="gIegJobIXU"
      data-id="gIegJobIXU"
      gp-data='{"position":{"desktop":"center","mobile":"center","tablet":"center"},"iconWidth":{"desktop":16,"mobile":16,"tablet":16}}'
       style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">
     
      <div class="gp-flex" style="--jc:left">
        <div
        class="gp-inline-flex gp-flex-col "
        style="--rg:16px">
        
           <div
            class="gp-icon-list-wrapper gp-flex gp-child-item-gIegJobIXU"
            style="--cg:0px;--ai:center;--ai-tablet:center;--ai-mobile:center"
            data-index="0"
          >
            <div class="gp-icon-list-icon gp-inline-flex gp-shrink-0 gp-overflow-hidden" style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:1px 1px 1px 1px;--bc:#000000;--bg:transparent;--pos:relative;--pos-tablet:relative;--pos-mobile:relative;--top:inherit;--top-tablet:inherit;--top-mobile:inherit">
              <span
                class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--size-desktop)] tablet:[&>svg]:!gp-h-[var(--size-tablet)] mobile:[&>svg]:!gp-h-[var(--size-mobile)] [&>svg]:!gp-w-[var(--size-desktop)] tablet:[&>svg]:!gp-w-[var(--size-tablet)] mobile:[&>svg]:!gp-w-[var(--size-mobile)]"
                style="--c:#57B8FF;--w:100%;--h:100%;--size-desktop:16px;--size-tablet:16px;--size-mobile:16px"
              >
               <svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817731477569896">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M156,128a28,28,0,1,1-28-28A28,28,0,0,1,156,128Z" /></svg>
              </span>
            </div>
             <div class=" gp-text-left gp-icon-list-text" style="--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:8px;--size-tablet:8px;--size-mobile:8px;--c:#ffffff">
                {{ section.settings.ggIegJobIXU_childItem_0 | replace: '$locationOrigin', locationOrigin }}
            </div>
            </div>
      </div>
      </div>
    </gp-icon-list>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-icon-list.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gK8ACvjAEg gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper  class="gNqXT2JvJU ">
      
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <gp-icon-list
      uid="gNqXT2JvJU"
      data-id="gNqXT2JvJU"
      gp-data='{"position":{"desktop":"center","mobile":"center","tablet":"center"},"iconWidth":{"desktop":16,"mobile":16,"tablet":16}}'
       style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">
     
      <div class="gp-flex" style="--jc:left">
        <div
        class="gp-inline-flex gp-flex-col "
        style="--rg:16px">
        
           <div
            class="gp-icon-list-wrapper gp-flex gp-child-item-gNqXT2JvJU"
            style="--cg:0px;--ai:center;--ai-tablet:center;--ai-mobile:center"
            data-index="0"
          >
            <div class="gp-icon-list-icon gp-inline-flex gp-shrink-0 gp-overflow-hidden" style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:1px 1px 1px 1px;--bc:#000000;--bg:transparent;--pos:relative;--pos-tablet:relative;--pos-mobile:relative;--top:inherit;--top-tablet:inherit;--top-mobile:inherit">
              <span
                class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--size-desktop)] tablet:[&>svg]:!gp-h-[var(--size-tablet)] mobile:[&>svg]:!gp-h-[var(--size-mobile)] [&>svg]:!gp-w-[var(--size-desktop)] tablet:[&>svg]:!gp-w-[var(--size-tablet)] mobile:[&>svg]:!gp-w-[var(--size-mobile)]"
                style="--c:#57B8FF;--w:100%;--h:100%;--size-desktop:16px;--size-tablet:16px;--size-mobile:16px"
              >
               <svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817731477569896">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M156,128a28,28,0,1,1-28-28A28,28,0,0,1,156,128Z" /></svg>
              </span>
            </div>
             <div class=" gp-text-left gp-icon-list-text" style="--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:8px;--size-tablet:8px;--size-mobile:8px;--c:#ffffff">
                {{ section.settings.ggNqXT2JvJU_childItem_0 | replace: '$locationOrigin', locationOrigin }}
            </div>
            </div>
      </div>
      </div>
    </gp-icon-list>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-icon-list.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    </div>
    </div>
   
    
    </div>
    </div>
   
    
      </div>
    
        
      </gp-hero-banner>
    </div>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-hero-banner.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gdiLWnbLl9">
    <div
      parentTag="Col"
        class="gdiLWnbLl9 "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--op-mobile:100%;--mb:24px;--mb-mobile:21px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggdiLWnbLl9_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gdiLWnbLl9">
        .gdiLWnbLl9 {

}
.gdiLWnbLl9 p {

}
      </style>
    
    <gp-tab data-id="g70CAjuywY" gp-data='{"setting":{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"borderTab":{"active":{"border":"solid","borderWidth":"1px","color":"#E0E0E0","isCustom":true,"position":"bottom","width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderWidth":"1px","color":"#E0E0E0","isCustom":true,"position":"all","width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderWidth":"1px","color":"#E0E0E0","isCustom":true,"position":"all","width":"1px 1px 1px 1px"}},"childItem":["<p>FEATURE</p>","<p>WHAT’S INCLUDE</p>","<p>SPECS</p>"],"labelAlign":{"desktop":"center"},"labelBgColor":{"active":"#EEEEEE"},"labelColor":{"active":"#242424","normal":"#242424"},"labelTypoV2":{"attrs":{"transform":"uppercase"},"custom":{"fontSize":{"desktop":"13px","mobile":"12px","tablet":"13px"},"fontStyle":"normal","fontVariants":["100","200","300","regular","500","600","700","800","900"],"fontWeight":"600","letterSpacing":"normal","lineHeight":{"desktop":"130%","mobile":"130%","tablet":"130%"}},"type":"paragraph-1"},"labelWidth":{"desktop":"200px"},"panelAlign":{"desktop":"left"},"panelFullWidth":{"desktop":true,"tablet":true},"panelWidth":{"desktop":"Auto"},"position":{"desktop":"top","mobile":"top","tablet":"top"},"translate":"childItem"},"builderProps":{"uid":"g70CAjuywY","builderData":{"advanced":{"border":{"desktop":{"normal":{"border":"none","borderType":"none","borderWidth":"1px","color":"#121212","isCustom":true,"width":"1px 1px 1px 1px"}}},"boxShadow":{"desktop":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}}},"d":{"desktop":true,"mobile":false,"tablet":true},"hasBoxShadow":{"desktop":{"normal":false}},"op":{"desktop":"100%","mobile":"100%","tablet":"100%"},"rounded":{"desktop":{"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"}}},"spacing-setting":{"desktop":{"link":false,"margin":{"bottom":"24px"},"padding":{"bottom":"16px"}}}},"childrens":["gwct2oSHm1","gNP-ejuyLA","gjiqBK8ExB"],"label":"Tab","settings":{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"borderTab":{"active":{"border":"solid","borderWidth":"1px","color":"#E0E0E0","isCustom":true,"position":"bottom","width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderWidth":"1px","color":"#E0E0E0","isCustom":true,"position":"all","width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderWidth":"1px","color":"#E0E0E0","isCustom":true,"position":"all","width":"1px 1px 1px 1px"}},"childItem":["<p>FEATURE</p>","<p>WHAT’S INCLUDE</p>","<p>SPECS</p>"],"labelAlign":{"desktop":"center"},"labelBgColor":{"active":"#EEEEEE"},"labelColor":{"active":"#242424","normal":"#242424"},"labelTypoV2":{"attrs":{"transform":"uppercase"},"custom":{"fontSize":{"desktop":"13px","mobile":"12px","tablet":"13px"},"fontStyle":"normal","fontVariants":["100","200","300","regular","500","600","700","800","900"],"fontWeight":"600","letterSpacing":"normal","lineHeight":{"desktop":"130%","mobile":"130%","tablet":"130%"}},"type":"paragraph-1"},"labelWidth":{"desktop":"200px"},"panelAlign":{"desktop":"left"},"panelFullWidth":{"desktop":true,"tablet":true},"panelWidth":{"desktop":"Auto"},"position":{"desktop":"top","mobile":"top","tablet":"top"},"translate":"childItem"},"styles":{},"tag":"Tabs","uid":"g70CAjuywY","type":"component"}}}'>
      <div
        
        data-id=""
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:none;--d-tablet:block;--op:100%;--op-mobile:100%;--op-tablet:100%;--mb:24px;--pb:16px;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gp-flex g70CAjuywY"
      >
        <style>
            .g70CAjuywY .gp-navs-tab.left p,
            .g70CAjuywY .gp-navs-tab.right p {
              word-wrap: break-word;
              white-space: break-spaces;
            }
            .g70CAjuywY .wrap-width_full_desktop_top_true { width: 100%; }
.g70CAjuywY .wrap-width_full_tablet_top_true { width: 100%; }
.g70CAjuywY .wrap-width_full_mobile_top_true { width: 100%; }

        </style>
        <div
          class="gp-flex gp-w-full  gp-flex-col mobile:gp-flex-col tablet:gp-flex-col"
        >
          <div
            class="gp-flex"
            style="--jc:left"
          >
            <ul
              class="gp-tab-header-list gp-flex gp-flex-wrap  gp-flex-row mobile:gp-flex-row tablet:gp-flex-row"
              style="--maxw:100%;--maxw-tablet:100%;--maxw-mobile:Auto"
            >
              
                  <li
                    class="gp-navs-tab gp-flex gp-flex-1 gp-cursor-pointer gp-py-2 gp-px-4"
                    aria-hidden
                    style="--bs:unset, --hvr-bs:unset, --hvr-bgc:unset, --hvr-c:unset,--hvr-bw:unset, --hvr-bc:unset"
                    key="g70CAjuywY"
                  >
                    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-cursor-pointer gp-overflow-hidden gp-whitespace-nowrap"
          style="--w:100%;--ta:center;--fs:normal;--ff:var(--g-font-body, body);--weight:600;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg70CAjuywY_childItem_0 }}</div>
      </div>
    </div>
    </gp-text>
    
                  </li>
                
                  <li
                    class="gp-navs-tab gp-flex gp-flex-1 gp-cursor-pointer gp-py-2 gp-px-4"
                    aria-hidden
                    style="--bs:unset, --hvr-bs:unset, --hvr-bgc:unset, --hvr-c:unset,--hvr-bw:unset, --hvr-bc:unset"
                    key="g70CAjuywY"
                  >
                    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-cursor-pointer gp-overflow-hidden gp-whitespace-nowrap"
          style="--w:100%;--ta:center;--fs:normal;--ff:var(--g-font-body, body);--weight:600;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg70CAjuywY_childItem_1 }}</div>
      </div>
    </div>
    </gp-text>
    
                  </li>
                
                  <li
                    class="gp-navs-tab gp-flex gp-flex-1 gp-cursor-pointer gp-py-2 gp-px-4"
                    aria-hidden
                    style="--bs:unset, --hvr-bs:unset, --hvr-bgc:unset, --hvr-c:unset,--hvr-bw:unset, --hvr-bc:unset"
                    key="g70CAjuywY"
                  >
                    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-cursor-pointer gp-overflow-hidden gp-whitespace-nowrap"
          style="--w:100%;--ta:center;--fs:normal;--ff:var(--g-font-body, body);--weight:600;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg70CAjuywY_childItem_2 }}</div>
      </div>
    </div>
    </gp-text>
    
                  </li>
                
            </ul>
          </div>
          <div
            class="gp-flex gp-flex-1 gp-min-w-0"
          >
            <div
              class="gp-tab-item-container gp-p-4 gp-pb-0"
              key="g70CAjuywY"
              style="--w:100%"
            >
            
  <div
    
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
    class="gp-tab-item gp-child-item-g70CAjuywY"
  >
    
       
      
    <div
      parentTag="TabItem" id="gxNMcfEjZG" data-id="gxNMcfEjZG"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--op-mobile:100%;--bblr:6px;--bbrr:6px;--btlr:6px;--btrr:6px;--mb:0px;--pt:8px;--pl:8px;--pb:8px;--pr:8px;--cg:8px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:#F3F3F3;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gxNMcfEjZG gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g9DkoDzsAf gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gN5Yk7X3-E">
    <div
      parentTag="Col"
        class="gN5Yk7X3-E "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggN5Yk7X3-E_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gN5Yk7X3-E">
        .gN5Yk7X3-E {

}
.gN5Yk7X3-E p {

}
      </style>
    
    </div>
    </div>
   
    
  </div>
  
  <div
    
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
    class="gp-tab-item gp-child-item-g70CAjuywY"
  >
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g0BpQmRAfa">
    <div
      parentTag="TabItem"
        class="g0BpQmRAfa "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg0BpQmRAfa_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-g0BpQmRAfa">
        .g0BpQmRAfa {

}
.g0BpQmRAfa p {

}
      </style>
    
  </div>
  
  <div
    
    
    class="gp-tab-item gp-child-item-g70CAjuywY"
  >
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gSOGYd74c5">
    <div
      parentTag="TabItem"
        class="gSOGYd74c5 "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--op-tablet:100%;--mb:0px;--mb-tablet:0px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggSOGYd74c5_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gSOGYd74c5">
        .gSOGYd74c5 {

}
.gSOGYd74c5 p {

}
      </style>
    
  </div>
  
            </div>
          </div>
        </div>
      </div>
    </gp-tab>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-tab.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  <div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--op-mobile:100%;--mb:24px;--mb-mobile:21px" class="gDHoYbVVAY ">
      
  {%- assign total_combinations = 1 -%}
  {%- for option in product.options_with_values -%}
    {%- assign total_combinations = total_combinations | times: option.values.size -%}
  {%- endfor -%}
  <gp-product-variants
    data-id="gDHoYbVVAY"
    
    has-pre-selected="true"
    gp-data='{
      "setting":{"blankText":"Please select an option","column":{"desktop":1},"combineFullWidth":{"desktop":true},"combineHeight":"45px","combineWidth":{"desktop":"100%"},"hasOnlyDefaultVariant":false,"hasPreSelected":true,"label":true,"layout":{"desktop":"vertical"},"optionAlign":{"desktop":"left"},"optionType":"singleOption","price":true,"showAsSwatches":true,"soldOutStyle":"disable","variantPresets":[{"isEdited":true,"optionName":"base","optionType":"rectangle_list","presets":{"color":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"hover":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"normal":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"45px","mobile":"45px","tablet":"45px"}},"dropdown":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"width":{"desktop":"100%","mobile":"100%","tablet":"100%"}},"image":{"height":"64px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"}},"image_shopify":{"height":"64px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"}},"rectangle_list":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":""}}}},{"isEdited":true,"optionName":"Color","optionType":"color","presets":{"color":{"height":"40px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#242424","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#242424","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"none","borderType":"style-2","borderWidth":"1px","color":"#7D7D7D","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"hover":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"normal":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"8px","width":{"desktop":"40px","mobile":"40px","tablet":"40px"}},"dropdown":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"width":{"desktop":"100%","mobile":"100%","tablet":"100%"}},"image":{"height":"64px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"}},"image_shopify":{"height":"64px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"}},"rectangle_list":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":""}}}}]},
      "styles":{"align":{"desktop":"left"},"dropdownItemWidth":{"desktop":"fill","mobile":"fill","tablet":"fill"},"fixedDropdownWidth":{"desktop":"240px"},"fullWidth":{"desktop":true},"labelColor":"text-2","labelGap":"12px","labelTypo":{"attrs":{"bold":true,"color":"#242424"},"custom":{"fontSize":{"desktop":"16px","mobile":"14px","tablet":"16px"},"fontStyle":"normal","fontVariants":["100","200","300","regular","500","600","700","800","900"],"fontWeight":"400","letterSpacing":"normal","lineHeight":{"desktop":"130%","mobile":"130%","tablet":"130%"}},"type":"paragraph-2"},"marginBottom":{"desktop":"var(--g-s-xl)","mobile":"var(--g-s-xl)"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionSpacing":"30px","optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"optionTypo":{"type":"paragraph-2"},"swatchAutoWidth":{"desktop":true},"swatchHeight":{"desktop":"45px"},"swatchItemWidth":{"desktop":"auto"},"swatchSpacing":"var(--g-s-m)","swatchWidth":{"desktop":"80px"},"width":{"desktop":"100%"}},
      "variants":{{product.variants | json | escape}},
      "optionsWithValues": {{product.options_with_values | json | escape}},
      "variantSelected": {{ variant | json | escape }},
      "variantInventoryQuantity": {{product.variants | map: 'inventory_quantity' | json | escape}},
      "variantInventoryPolicy": {{product.variants | map: 'inventory_policy' | json | escape}},
      "moneyFormat": {{shop.money_format | json | escape}},
      "productId": {{product.id | json | escape}},
      "productUrl": {{product.url | json | escape}},
      "productHandle": {{product.handle | json | escape}},
      "displayState": {"desktop":true,"mobile":true,"tablet":true},
      "totalVariantCombinations": {{total_combinations}},
      "firstAvailableVariant": {{product.selected_or_first_available_variant | json | escape}}
    }
  '>
    {%- assign options = product.options_with_values -%}
    {%- assign variants = product.variants -%}
    {%- if options.size == 0 or options.size == 1 and variants.size == 1 and variants[0].title == 'Default Title' and variants[0].option1 == 'Default Title' -%}
      <div></div>
    {% else %}
      <div
      class="gp-grid !gp-ml-0"
      style="--gtc:repeat(1, minmax(0, 1fr));--ta:left;--w:100%;--w-tablet:100%;--w-mobile:100%;--rg:var(--g-s-xl);--rg-mobile:var(--g-s-xl)"
    >
      
      {% assign presets = "base($2)rectangle_list($1)Color($2)color" | split: '($1)' %}
      {% assign hiddenPresetOptions = "" | split: ',' %}
      {%- for option in options -%}
        <div
        option-name="{{option.name | escape}}"
        class="gp-flex variant-inside gp-flex-col gp-items-start"

        >
          {% assign showVariantClass = 'variant-display' %}
          {% assign optionName = option.name %}
          {% for preset in presets %}
            {% assign presetDetail = preset | split: '($2)' %}
            {% if presetDetail[1] == 'dropdown' and presetDetail[0] == optionName %}
              {% assign showVariantClass = '' %}
              {% break %}
            {% endif %}
          {% endfor %}

          
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        style="--mb:12px;--mr:0px;--mb-tablet:12px;--mr-tablet:0px;--mb-mobile:12px;--mr-mobile:0px"
      >
      <div  >
        <legend
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] {{showVariantClass}}"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--weight:bold;--c:#242424;word-break:break-word;overflow:hidden"
        >{{option.name}}</legend>
      </div>
    </div>
    </gp-text>
    

          <div
              variant-option-name="{{option.name | escape}}"
              class="gp-justify-start gp-flex gp-w-full gp-flex-wrap gp-items-center variant-option-group"
              style="--rg:var(--g-s-m);--cg:var(--g-s-m)"
            >
              {%- assign values = option.values -%}
              {%- assign rootForloop = forloop.index0 -%}
              {%- if option.position == 1 -%}
                {%- assign selectedValue = variant.option1 -%}
              {%- elsif option.position == 2 -%}
                {%- assign selectedValue = variant.option2 -%}
              {%- else -%}
                {%- assign selectedValue = variant.option3 -%}
              {%- endif -%}
              
              
    {% assign optionRendered = false %}
    {%  assign swatches = shop.metafields.GEMPAGES.swatches %}
    {%  assign swatchesItems = swatches | split: '($1)'  %}
    {% for swatchesItem in swatchesItems %}
      {% assign colorArraysString = "" %}
      {% assign labelsString = "" %}
      {% assign imageUrlsString = "" %}

      {%  assign attrItems = swatchesItem | split: '($3)'  %}
      {% for attrItem in attrItems %}
        {%  assign attrs = attrItem | split: '($2)'  %}


          {% assign optionKey = attrs[0] %}
          {% assign optionValue = attrs[1] %}
          {% if optionKey == 'optionTitle' %}
                {% assign optionTitle = optionValue %}
              {% elsif optionKey == 'optionType' %}
                {% assign optionType = optionValue %}
            {% endif %}


            {% if optionKey == 'optionValues' %}

              {% assign opValueItems = optionValue | split: '($4)'  %}
              {% for opValueItem in opValueItems %}
                {% assign opValueItemAttrs = opValueItem | split: '($6)'  %}
                {% for opValueItemAttr in opValueItemAttrs %}
                  {% assign attrs = opValueItemAttr | split: '($5)'  %}
                  {% assign opValueItemKey = attrs[0] %}
                  {% assign opValueItemValue = attrs[1] %}

                  {% if opValueItemKey == 'label' %}
                    {% assign labelsString = labelsString | append: opValueItemValue %}
                    {% assign labelsString = labelsString | append: "($8)" %}
                  {% endif %}

                  {% if opValueItemKey == 'colors' %}
                    {% assign colorArraysString = colorArraysString | append: opValueItemValue %}
                    {% assign colorArraysString = colorArraysString | append: "($8)" %}
                  {% endif %}

                  {% if opValueItemKey == 'imageUrl' %}
                    {% assign imageUrlsString = imageUrlsString | append: opValueItemValue %}
                    {% assign imageUrlsString = imageUrlsString | append: "($8)" %}

                  {% endif %}
                {% endfor %}
              {% endfor %}
            {% endif %}

      {% endfor %}
      {% assign labels = labelsString | split: '($8)' %}
      {% assign colorStrings = colorArraysString | split: '($8)' %}
      {% assign imageUrls = imageUrlsString | split: '($8)' %}

      {% if optionTitle == option.name %}
      {% assign variantPresetString = "base($1)rectangle_list($2)Color($1)color" %}
      {% assign optionName = option.name | replace: "'", "&apos;" | replace: '"', "&quot;" %}
        {% assign items = variantPresetString | split:'($2)' %}
        {% assign type = 'dropdown' %}
        {%- for item in items -%}
          {% assign itemPreset = item | split:'($1)' %}
          {% if itemPreset[0] == optionName %}
            {% assign type = itemPreset[1] %}
          {% endif %}
          {% if itemPreset[0] == "base" %}
            {% assign type = itemPreset[1] %}
          {% endif %}
        {%- endfor -%}
        {% assign optionRendered = true %}
        {%- for value in values -%}
          
    {%- liquid
      assign option_disabled = true
      for variantItem in product.variants
        case option.position
          when 1
            if variantItem.available and variantItem.option1 == value
              assign option_disabled = false
            endif
          when 2
            if variantItem.available and variantItem.option2 == value
              assign option_disabled = false
            endif
          when 3
            if variantItem.available and variantItem.option3 == value
              assign option_disabled = false
            endif
        endcase
      endfor
    -%}

  
  <label
  id="{{option.name | escape}}-{{value | escape}}"
  for="{{product.id}}-gDHoYbVVAY-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
  class="gp-group gp-relative option-item gp-child-item-gDHoYbVVAY" 
>
  <div class="gp-invisible mobile:gp-overflow-hidden mobile:!gp-max-w-[0px] !gp-max-w-[150px] gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-absolute gp-bottom-[calc(100%+20px)] gp-text-[12px] group-hover:gp-visible gp-w-max gp-bg-[#333333] gp-text-[#F9F9F9] mobile:gp-px-[0px] gp-px-[8px] gp-py-[4px] gp-rounded-[8px] gp-mb-[-10px] after:gp-content-[''] after:gp-absolute mobile:after:gp-p-[0px] after:gp-p-[7px] after:gp-left-0 after:gp-w-full after:gp-bottom-[-10px]">
    <p class="gp-text-[#F9F9F9]">{{value | escape}}</p>
    <svg class="gp-absolute gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-z-10 gp-bottom-[-4px]" width="8" height="4" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8 10L0 0L16 1.41326e-06L8 10Z" fill="#333333"/>
    </svg>
  </div>
  <input
    checked="{%- if option.selected_value == value -%}true{% else %}false{%- endif -%}"
    name="{{product.id}}-options-{{option.name | escape}}"
    id="{{product.id}}-gDHoYbVVAY-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
    value="{{value | escape}}"
    type="radio"
    class="gp-sr-only gp-absolute gp-bottom-0 gp-right-0"
  />
  <span class="gp-sr-only">{{value | escape}}</span>
  <div   
  option-name="{{option.name | escape}}"
  option-name-value="{{value | escape}}"
  data-gp-option-value-id="{{value.id}}"
  class="option-item-inner gp-w-auto gp-h-auto {%- if option_disabled == true -%}gp-opacity-20{%- endif -%}">
  
    {% case type %}
      {% when "rectangle_list" %}
      <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bg: var(--g-c-bg-3, bg-3);--bs: solid;--bw: 2px 2px 2px 2px;--bc: var(--g-c-line-3, line-3);--c: var(--g-c-text-2, text-2);--shadow: none;--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--h: 45px;
      {%- else -%}
        --bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--shadow: none;--hvr-shadow: none;--hvr-bg: var(--g-c-bg-3, bg-3);--bg: var(--g-c-bg-3, bg-3);--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--hvr-c: var(--g-c-text-2, text-2);--c: var(--g-c-text-2, text-2);--h: 45px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="rectangle_list"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    
      <div class="option-value gp-overflow-hidden gp-relative 
      "
      style="--pl: 16px;--pr: 16px;--pt: 8px;--pb: 8px;">
      
      <span class="gp-text-center gp-g-paragraph-2" >{{value}}</span>
      </div>
    
  </div>
      {% when "color" %}
      
    {% assign colorsString = null %}
    {% assign colors = null %}
    {% for label in labels %}
      {% if label == value %}
        {% assign colorsString = colorStrings[forloop.index0] %}
      {% endif %}
    {% endfor %}
    {% if colorsString != null %}
      {% assign colors = colorsString | split: '($7)' %}
    {% endif %}
    <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2 gp-p-[4px]
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2 gp-p-[4px]
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bs: solid;--bw: 2px 2px 2px 2px;--bc: var(--g-c-line-3, line-3);--shadow: none;--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--h: 45px;--w: 45px;
      {%- else -%}
        --bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;--shadow: none;--hvr-shadow: none;--h: 45px;--w: 45px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="color"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    {%- if colors != null and colors.size > 0 -%} 
      {%- for color in colors -%}
      {% assign backgroundType = "background-color" %}
        {% if color contains "linear-gradient" %}
          {% assign backgroundType = "background-image" %}
        {% endif %}
        <div
          class="gp-relative gp-h-full gp-w-full gp-min-w-fit gp-flex gp-color-circle before:gp-hidden before:gp-absolute before:gp-top-[50%] before:-gp-rotate-45 before:gp-border-t before:gp-z-1 before:gp-content-[''] before:gp-w-full"
          data-test="{{ backgroundType }}: {{ color }}"
          style="{{ backgroundType }}: {{ color }}; 
    {%- if selectedValue == value -%}
      --d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: none;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;
    {%- else -%}
      --d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;
    {%- endif -%}
    "></div>
      {%- endfor -%}
       {% else %} 
      <div class="option-value gp-overflow-hidden gp-relative 
      "
      style="
    {%- if selectedValue == value -%}
      --d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: none;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;
    {%- else -%}
      --d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;
    {%- endif -%}
    ">
      
      <span class="gp-text-center gp-overflow-hidden gp-text-ellipsis gp-px-1 gp-g-paragraph-2" >{{value}}</span>
      </div>
     {%- endif -%}
  </div>
  
      {% when "image_shopify" %}
      
      {% assign imageUrl = null %}
      {% for variant in variants %}
        {% assign valueIncludesSelectedOption = false %}
        {% for item in variant.options %}
          {% if item == value %}
          {% assign valueIncludesSelectedOption = true %}
          {% endif %}
        {% endfor %}
        {% if valueIncludesSelectedOption and variant.featured_image or variant.featured_media%}
          {% unless imageUrl %}
            {% if variant.featured_media %}
              {% assign imageUrl = variant.featured_media.preview_image.src | product_img_url: '200x'  %}
            {% else %}
              {% assign imageUrl = variant.featured_image.src | product_img_url: '200x'  %}
            {% endif %}
          {% endunless %}
        {% endif %}
      {% endfor %}
      <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bg: var(--g-c-bg-3, bg-3);--bs: solid;--bw: 2px 2px 2px 2px;--bc: var(--g-c-line-3, line-3);--shadow: none;--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--h: 64px;--w: 64px;
      {%- else -%}
        --bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--shadow: none;--hvr-shadow: none;--hvr-bg: var(--g-c-bg-3, bg-3);--bg: var(--g-c-bg-3, bg-3);--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--h: 64px;--w: 64px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="image_shopify"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    {%- if imageUrl != null and imageUrl != "" -%} 
      <img style="width: 64px; height: 64px" class="gp-object-cover gp-rounded-none" src="{{imageUrl}}" alt="" />
     {% else %} 
      <div class="option-value gp-overflow-hidden gp-relative gp-flex gp-flex-col gp-justify-center gp-items-center gp-gap-[6px]
      "
      >
      <svg width="15" height="12" viewBox="0 0 15 12" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="2" cy="2" r="2" fill="#D6D6D6"></circle><path d="M0.196854 10.6453L2.40968 8.05946C2.4846 7.97175 2.57719 7.9008 2.68141 7.85124C2.78562 7.80168 2.89913 7.77461 3.01452 7.77181C3.12991 7.76902 3.2446 7.79055 3.3511 7.835C3.45761 7.87945 3.55353 7.94583 3.63262 8.02981L4.66733 9.12714L8.71205 4.29464C8.79123 4.19995 8.89077 4.1243 9.00325 4.07333C9.11573 4.02236 9.23827 3.99737 9.36176 4.00022C9.48524 4.00307 9.6065 4.03369 9.71651 4.08979C9.82651 4.1459 9.92245 4.22605 9.99717 4.32429L14.8329 10.6772C14.9254 10.7989 14.982 10.9441 14.9964 11.0962C15.0107 11.2484 14.9823 11.4016 14.9144 11.5385C14.8464 11.6754 14.7415 11.7907 14.6115 11.8713C14.4815 11.952 14.3316 11.9948 14.1786 11.995L0.822048 12C0.664945 11.9999 0.511148 11.9549 0.378853 11.8703C0.246557 11.7857 0.141299 11.6649 0.0755311 11.5224C0.00976323 11.3799 -0.013762 11.2216 0.00773837 11.0661C0.0292388 10.9107 0.0948652 10.7646 0.196854 10.6453Z" fill="#D6D6D6"></path></svg>
      <span class="gp-text-center gp-overflow-hidden gp-text-ellipsis gp-px-1 gp-g-paragraph-2" >{{value}}</span>
      </div>
     {%- endif -%}
  </div>
  
      {% when "image" %}
      
    {% assign imageUrl = null %}
    {% for label in labels %}
    {% if label == value %}
      {% assign imageUrl = imageUrls[forloop.index0] %}
    {% endif %}
    {% endfor %}
    <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bg: var(--g-c-bg-3, bg-3);--bs: solid;--bw: 2px 2px 2px 2px;--bc: var(--g-c-line-3, line-3);--shadow: none;--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--h: 64px;--w: 64px;
      {%- else -%}
        --bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--shadow: none;--hvr-shadow: none;--hvr-bg: var(--g-c-bg-3, bg-3);--bg: var(--g-c-bg-3, bg-3);--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--h: 64px;--w: 64px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="image"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    {%- if imageUrl != null and imageUrl != "" -%} 
      <img style="width: 64px; height: 64px" class="gp-object-cover gp-rounded-none" src="{{imageUrl}}" alt="" />
     {% else %} 
      <div class="option-value gp-overflow-hidden gp-relative gp-flex gp-flex-col gp-justify-center gp-items-center gp-gap-[6px]
      "
      >
      <svg width="15" height="12" viewBox="0 0 15 12" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="2" cy="2" r="2" fill="#D6D6D6"></circle><path d="M0.196854 10.6453L2.40968 8.05946C2.4846 7.97175 2.57719 7.9008 2.68141 7.85124C2.78562 7.80168 2.89913 7.77461 3.01452 7.77181C3.12991 7.76902 3.2446 7.79055 3.3511 7.835C3.45761 7.87945 3.55353 7.94583 3.63262 8.02981L4.66733 9.12714L8.71205 4.29464C8.79123 4.19995 8.89077 4.1243 9.00325 4.07333C9.11573 4.02236 9.23827 3.99737 9.36176 4.00022C9.48524 4.00307 9.6065 4.03369 9.71651 4.08979C9.82651 4.1459 9.92245 4.22605 9.99717 4.32429L14.8329 10.6772C14.9254 10.7989 14.982 10.9441 14.9964 11.0962C15.0107 11.2484 14.9823 11.4016 14.9144 11.5385C14.8464 11.6754 14.7415 11.7907 14.6115 11.8713C14.4815 11.952 14.3316 11.9948 14.1786 11.995L0.822048 12C0.664945 11.9999 0.511148 11.9549 0.378853 11.8703C0.246557 11.7857 0.141299 11.6649 0.0755311 11.5224C0.00976323 11.3799 -0.013762 11.2216 0.00773837 11.0661C0.0292388 10.9107 0.0948652 10.7646 0.196854 10.6453Z" fill="#D6D6D6"></path></svg>
      <span class="gp-text-center gp-overflow-hidden gp-text-ellipsis gp-px-1 gp-g-paragraph-2" >{{value}}</span>
      </div>
     {%- endif -%}
  </div>
  
      {% else %}
    {% endcase %}
      

     
    
    
  </div>
</label>
        {%- endfor -%}
        {% if type == 'dropdown' %}
        
    <select
    aria-label={{option.name | escape}}
    autocomplete="off"
    id="p-variant-dropdown-{{option.position}}"
    name="{%- if option -%}{{option.name | escape}}{% else %}Select Option{%- endif -%}"
    option-data="{{option.name}}"
    option-type="{{optionType}}"
    option-renderer="{{optionRendered}}"
    class="gp-truncate gp-bg-auto gp-pl-4 gp-pr-6 gp-outline-none dropdown-option-item gp-border-g-line-2 hover:gp-border-g-line-3 gp-g-paragraph-2 active:gp-text-g-text-2 hover:gp-text-g-text-2 gp-text-g-text-2 active:gp-bg-g-bg-3 hover:gp-bg-g-bg-3 gp-bg-g-bg-3 gp-outline-none gp-shadow-none"
 
    style="--shadow:none;--hvr-shadow:none;--bg:var(--g-c-bg-3, bg-3);--bs:solid;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-2, line-2);--hvr-c:var(--g-c-text-2, text-2);--c:var(--g-c-text-2, text-2);--hvr-bg:var(--g-c-bg-3, bg-3);--h:45px;--w:100%;--w-tablet:100%;--w-mobile:100%;--hvr-bs:solid;--hvr-bw:1px 1px 1px 1px;--hvr-bc:var(--g-c-line-3, line-3);--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--hvr-bblr:0px;--hvr-bbrr:0px;--hvr-btlr:0px;--hvr-btrr:0px;appearance:none;background-image:url(https://cdn.shopify.com/s/files/1/1827/4239/t/1/assets/ico-select.svg?v=155563818344741998551488860031);background-repeat:no-repeat;background-position:right 16px center"
  >
  
  {%- for value in values -%}
          {%- liquid
            assign option_disabled = true
            for variantItem in product.variants
              case option.position
                when 1
                  if variantItem.available and variantItem.option1 == value
                    assign option_disabled = false
                  endif
                when 2
                  if variantItem.available and variantItem.option2 == value
                    assign option_disabled = false
                  endif
                when 3
                  if variantItem.available and variantItem.option3 == value
                    assign option_disabled = false
                  endif
              endcase
            endfor
          -%}
          {%- if option_disabled == true -%}
                    {%- if value == selectedValue -%}
                      <option disabled selected 
                        option-position="{{option.position}}"
                        
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}" 
    data-gp-option-value-id="{{value.id}}" 
    data-gp-option-available="{{value.available}}"
    value="{{value | escape}}" 
    class="option-value-wrapper" 
    key="{{value | escape}}"
  >
                          {{value}} 
                      </option>
                    {% else %}
                        <option 
                        disabled 
                        
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}" 
    data-gp-option-value-id="{{value.id}}" 
    data-gp-option-available="{{value.available}}"
    value="{{value | escape}}" 
    class="option-value-wrapper" 
    key="{{value | escape}}"
  >
                          {{value}} 
                        </option>
                    {%- endif -%}
                {%- else -%}
                {%- if value == selectedValue -%}
                  <option selected   
                      option-position="{{option.position}}" 
                      
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}" 
    data-gp-option-value-id="{{value.id}}" 
    data-gp-option-available="{{value.available}}"
    value="{{value | escape}}" 
    class="option-value-wrapper" 
    key="{{value | escape}}"
  >
                      {{value}}
                  </option>
                  {% else %}
                  <option
                      
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}" 
    data-gp-option-value-id="{{value.id}}" 
    data-gp-option-available="{{value.available}}"
    value="{{value | escape}}" 
    class="option-value-wrapper" 
    key="{{value | escape}}"
  
                      option-position="{{option.position}}">
                      {{value}} 
                  </option>
                  {%- endif -%}
                {%- endif -%}
         
        {%- endfor -%}
  </select>
    
        {% endif %}
      {% endif %}
    {% endfor %}

    {% if optionRendered == false %}
      
    {%- for value in values -%}
      
    {%- liquid
      assign option_disabled = true
      for variantItem in product.variants
        case option.position
          when 1
            if variantItem.available and variantItem.option1 == value
              assign option_disabled = false
            endif
          when 2
            if variantItem.available and variantItem.option2 == value
              assign option_disabled = false
            endif
          when 3
            if variantItem.available and variantItem.option3 == value
              assign option_disabled = false
            endif
        endcase
      endfor
    -%}

  
  <label
  id="{{option.name | escape}}-{{value | escape}}"
  for="{{product.id}}-gDHoYbVVAY-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
  class="gp-group gp-relative option-item gp-child-item-gDHoYbVVAY" 
>
  <div class="gp-invisible mobile:gp-overflow-hidden mobile:!gp-max-w-[0px] !gp-max-w-[150px] gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-absolute gp-bottom-[calc(100%+20px)] gp-text-[12px] group-hover:gp-visible gp-w-max gp-bg-[#333333] gp-text-[#F9F9F9] mobile:gp-px-[0px] gp-px-[8px] gp-py-[4px] gp-rounded-[8px] gp-mb-[-10px] after:gp-content-[''] after:gp-absolute mobile:after:gp-p-[0px] after:gp-p-[7px] after:gp-left-0 after:gp-w-full after:gp-bottom-[-10px]">
    <p class="gp-text-[#F9F9F9]">{{value | escape}}</p>
    <svg class="gp-absolute gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-z-10 gp-bottom-[-4px]" width="8" height="4" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8 10L0 0L16 1.41326e-06L8 10Z" fill="#333333"/>
    </svg>
  </div>
  <input
    checked="{%- if option.selected_value == value -%}true{% else %}false{%- endif -%}"
    name="{{product.id}}-options-{{option.name | escape}}"
    id="{{product.id}}-gDHoYbVVAY-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
    value="{{value | escape}}"
    type="radio"
    class="gp-sr-only gp-absolute gp-bottom-0 gp-right-0"
  />
  <span class="gp-sr-only">{{value | escape}}</span>
  <div   
  option-name="{{option.name | escape}}"
  option-name-value="{{value | escape}}"
  data-gp-option-value-id="{{value.id}}"
  class="option-item-inner gp-w-auto gp-h-auto {%- if option_disabled == true -%}gp-opacity-20{%- endif -%}">
  
    <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bg: var(--g-c-bg-3, bg-3);--bs: solid;--bw: 2px 2px 2px 2px;--bc: var(--g-c-line-3, line-3);--c: var(--g-c-text-2, text-2);--shadow: none;--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--h: 45px;--w: 80px;
      {%- else -%}
        --bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--shadow: none;--hvr-shadow: none;--hvr-bg: var(--g-c-bg-3, bg-3);--bg: var(--g-c-bg-3, bg-3);--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--hvr-c: var(--g-c-text-2, text-2);--c: var(--g-c-text-2, text-2);--h: 45px;--w: 80px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="rectangle_list"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    
      <div class="option-value gp-overflow-hidden gp-relative 
      "
      style="--pl: 16px;--pr: 16px;--pt: 8px;--pb: 8px;">
      
      <span class="gp-text-center gp-g-paragraph-2" >{{value}}</span>
      </div>
    
  </div>
    
  </div>
</label>
    {%- endfor -%}
    
    {% endif %}
    
          </div>
      </div>
      {%- endfor -%}
    
    </div>
    {%- endif -%}

  </gp-product-variants>
  <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-product-variant-v3.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
   
      </div>
       
      
    <div
      parentTag="Col" id="gdR1CScooE" data-id="gdR1CScooE"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:12px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 9fr);--gtc-tablet:minmax(0, 4fr) minmax(0, 8fr);--gtc-mobile:minmax(0, 4fr) minmax(0, 8fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gdR1CScooE gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gG1VFmODu0 gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:solid;--bw:1px 1px 1px 1px;--bc:#E0E0E0;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:6px;--bbrr:6px;--btlr:6px;--btrr:6px" class="gC-jqH5AiO ">
      
    {%- liquid
      assign current_variant = product.selected_or_first_available_variant
      assign available = current_variant.available | default: false
    -%}
      <gp-product-quantity
        data-id="gC-jqH5AiO"
        data-disabled="{%- if available -%} false {%- else -%} true {%- endif -%}"
        data-price="false"
        class="quantityClass gp-relative gp-inline-flex gp-w-full gp-bg-transparent gp-transition-all gp-duration-150 data-[disabled=true]:gp-opacity-60 "
        style="--h:48px;--h-mobile:42px;--jc:left"
      >

      
          <button
            title="Decrement"
            aria-label="decrement"
            {% if available == false %} disabled {% endif %}
            class="gp-bg-g-bg-3 gp-minus gp-flex gp-aspect-square gp-h-full gp-cursor-pointer gp-items-center gp-justify-center gp-outline-none disabled:gp-cursor-not-allowed"
            style="--w:48px;--w-mobile:42px;--hvr-bg:#ffffff;--bg:var(--g-c-bg-3, bg-3);--c:#575757;--bs:none;--hvr-bs:none;--bw:1px 1px 1px 1px;--hvr-bw:1px 1px 1px 1px;--bc:#7D7D7D;--hvr-bc:#121212;--bblr:6px;--bbrr:auto;--btlr:6px;--btrr:auto"
          >
            <span class="gp-inline-flex gp-items-center gp-justify-center" style="--w:20px">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="gp-h-full gp-w-full"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 12H6" />
              </svg>
            </span>
          </button>
      
        <input
          type="text"
          name="product-quantity"
          {% if available == false %} disabled {% endif %}
          class="gp-bg-g-bg-3 !gp-border-x-0 gp-px-4 gp-flex gp-shadow-none gp-appearance-none gp-items-center gp-border-y gp-text-center gp-outline-none gp-transition-all gp-duration-150 hover:gp-text-black disabled:gp-pointer-events-none gp-h-auto gp-rounded-none gp-shrink-[99999] gp-w-full gp-min-w-[45px]"
          style="--maxw:50px;--maxw-tablet:50px;--maxw-mobile:50px;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;--bg:var(--g-c-bg-3, bg-3);--bs:none;--hvr-bs:none;--bw:1px 1px 1px 1px;--hvr-bw:1px 1px 1px 1px;--bc:#7D7D7D;--hvr-bc:#121212"
          aria-label="Quantity"
          inputmode="numeric"
          min="{{ current_variant.quantity_rule.min }}"
          step="{{ current_variant.quantity_rule.increment }}"
          value="1"
          autocomplete='off'
        />

        
          <button
            {% if available == false %} disabled {% endif %}
            aria-label="increment"
            title="Increment"
            class="gp-bg-g-bg-3 gp-plus gp-flex gp-aspect-square gp-h-full gp-cursor-pointer gp-items-center gp-justify-center gp-outline-none gp-transition-all gp-duration-150 disabled:gp-pointer-events-none"
            style="--w:48px;--w-mobile:42px;--hvr-bg:#ffffff;--bg:var(--g-c-bg-3, bg-3);--c:#575757;--bs:none;--hvr-bs:none;--bw:1px 1px 1px 1px;--hvr-bw:1px 1px 1px 1px;--bc:#7D7D7D;--hvr-bc:#121212;--bblr:auto;--bbrr:6px;--btlr:auto;--btrr:6px"
          >
            <span class="gp-inline-flex gp-items-center gp-justify-center" style="--w:20px">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="gp-h-full gp-w-full"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
            </span>
          </button>
        
      </gp-product-quantity>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-product-quantity.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
      </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gSGGdrGWxt gp-relative gp-flex gp-flex-col"
    >
      
   {%- liquid
      assign inventory_quantity = variant.inventory_quantity | default: 0
    -%}
    <gp-product-button
      class="gp-product-button"
      gp-data-wrapper="true"
      gp-label-out-of-stock="{{section.settings.ggRcSTlFxGT_outOfStockLabel}}"
      gp-label-unavailable="{{section.settings.ggRcSTlFxGT_unavailableLabel}}"
      gp-data='{"setting":{"actionEffect":"open-cart-drawer","errorMessage":"Cannot add product to cart","successMessage":"Add product to cart successfully","enableSuccessMessage":true,"enableErrorMessage":true,"label":"Add to cart","outOfStockLabel":"Out of stock","errorType":"built-in","customURL":{"link":"/cart","target":"_self"}},"styles":{"errorTypo":{"attrs":{"color":"error"},"type":"paragraph-2"},"successTypo":{"attrs":{"color":"success"},"type":"paragraph-2"}},"disabled":"{{variant.available}}","variantID":"{{variant.id}}","totalVariant":"{{product.variants.size}}"}' 
       gp-href="{{ request.origin }}{{ routes.root_url | split: '/' | join: '/' }}/cart"
       data-variant-selection-required-message="{{}}"
    >
        
  <gp-button >
  <div
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--op-mobile:100%;--mb:0px;--ta:left"
    class="{% unless variant.available %} !gp-hidden {% endunless %}"
  >
    <style>
    .gRcSTlFxGT.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 6px;
      border-bottom-right-radius: 6px;
      border-top-left-radius: 6px;
      border-top-right-radius: 6px;
      
    }

    .gRcSTlFxGT:hover::before {
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
    }

    .gRcSTlFxGT:hover .gp-button-icon {
      color: undefined;
    }

     .gRcSTlFxGT .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gRcSTlFxGT:hover .gp-button-price {
      color: undefined;
    }

    .gRcSTlFxGT .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gRcSTlFxGT .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gRcSTlFxGT:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="submit" data-id="gRcSTlFxGT" aria-label="Add to cart"
      name="add" gp-data-hidden="{% unless variant.available %}true{% endunless %}"
      data-state="idle"
      class="gRcSTlFxGT gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-atc tcustomizer-submit-button"
      style="--hvr-bg:#575757;--bg:#242424;--bblr:6px;--bbrr:6px;--btlr:6px;--btrr:6px;--shadow:none;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:14px;--pb:14px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:11px;--pb-mobile:11px;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase"
    >
        <svg
          class="gp-invisible gp-absolute gp-h-5 gp-w-5 group-data-[state=loading]/button:gp-animate-spin group-data-[state=loading]/button:gp-visible"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="gp-opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="gp-opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggRcSTlFxGT_label }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

        
  <gp-button >
  <div
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--op-mobile:100%;--mb:0px;--ta:left"
    class="{% if variant.available %} !gp-hidden {% endif %}"
  >
    <style>
    .gRcSTlFxGT-sold-out.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 6px;
      border-bottom-right-radius: 6px;
      border-top-left-radius: 6px;
      border-top-right-radius: 6px;
      
    }

    .gRcSTlFxGT-sold-out:hover::before {
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
    }

    .gRcSTlFxGT-sold-out:hover .gp-button-icon {
      color: undefined;
    }

     .gRcSTlFxGT-sold-out .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gRcSTlFxGT-sold-out:hover .gp-button-price {
      color: undefined;
    }

    .gRcSTlFxGT-sold-out .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gRcSTlFxGT-sold-out .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gRcSTlFxGT-sold-out:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="button" data-id="gRcSTlFxGT" aria-label="{{section.settings.ggRcSTlFxGT_outOfStockLabel}}"
      gp-data-hidden="{% if variant.available %}true{% endif %}"
      data-state="idle"
      class="gRcSTlFxGT-sold-out gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-sold-out btn-disabled gp-opacity-30 gp-cursor-default"
      style="--hvr-bg:#575757;--bg:#242424;--bblr:6px;--bbrr:6px;--btlr:6px;--btrr:6px;--shadow:none;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:14px;--pb:14px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:11px;--pb-mobile:11px;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{section.settings.ggRcSTlFxGT_outOfStockLabel}}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

    </gp-product-button>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-product-button.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
          {%- endform -%}
        </product-form>
      </gp-product>
      {%- assign product = gpBkProduct -%}
    {% else %}
      <div class="gp-text-center">{{ "gempages.Product.product_not_found" | t }}</div>
    {%- endif -%}
     <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-product.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 1",
    "tag": "section",
    "class": "gps-575054661598315749 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=575053851124565104&sectionId=575054661598315749)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggXGwA-vgFp_template","label":"ggXGwA-vgFp_template","default":"\n      <mark class='gp-text-white gp-py-1 gp-px-2 gp-mr-1 gp-inline-block gp-my-[1px] gp-bg-g-text-1'>\n    HURRY!</mark> ONLY <span class='gp-text-g-text-1'><$quantity$></span> LEFT"},{"type":"html","id":"ggXGwA-vgFp_outOfStockMessage","label":"ggXGwA-vgFp_outOfStockMessage","default":"OUT OF STOCK"},{"type":"html","id":"ggXGwA-vgFp_continueSelling","label":"ggXGwA-vgFp_continueSelling","default":"\n      <mark class='gp-text-white gp-py-1 gp-px-2 gp-mr-1 gp-inline-block gp-my-[1px] gp-bg-g-text-1'>\n    Restock soon!</mark> PREORDER NOW"},{"type":"html","id":"ggXGwA-vgFp_unlimitedQuantityMessage","label":"ggXGwA-vgFp_unlimitedQuantityMessage","default":"\n      <mark class='gp-text-white gp-py-1 gp-px-2 gp-mr-1 gp-inline-block gp-my-[1px] gp-bg-g-text-1'>\n    HURRY!</mark> LET BUY NOW"},{"type":"html","id":"ggmOwR35HA5_dayLabel","label":"ggmOwR35HA5_dayLabel","default":"Days"},{"type":"html","id":"ggmOwR35HA5_hourLabel","label":"ggmOwR35HA5_hourLabel","default":"Hours"},{"type":"html","id":"ggmOwR35HA5_minuteLabel","label":"ggmOwR35HA5_minuteLabel","default":"Minutes"},{"type":"html","id":"ggmOwR35HA5_secondLabel","label":"ggmOwR35HA5_secondLabel","default":"Seconds"},{"type":"html","id":"ggmOwR35HA5_weekLabel","label":"ggmOwR35HA5_weekLabel","default":"Weeks"},{"type":"html","id":"ggoGCUjIHky_defaultLabel","label":"ggoGCUjIHky_defaultLabel","default":"Copy"},{"type":"html","id":"gg7VwU_pVAI_text","label":"gg7VwU_pVAI_text","default":"<p>2,500+ Reviews!</p>"},{"type":"html","id":"ggW4qkFIgkt_text","label":"ggW4qkFIgkt_text","default":"<p>Enjoy an amazing</p>"},{"type":"html","id":"ggpeoYAYmGJ_text","label":"ggpeoYAYmGJ_text","default":"<p>10% off!</p>"},{"type":"html","id":"gghsqPAOP8G_childItem_0","label":"gghsqPAOP8G_childItem_0","default":"<p>Best Price Ever</p>"},{"type":"html","id":"ggIegJobIXU_childItem_0","label":"ggIegJobIXU_childItem_0","default":"<p>Fast Shipping</p>"},{"type":"html","id":"ggNqXT2JvJU_childItem_0","label":"ggNqXT2JvJU_childItem_0","default":"<p>Trusted by Creators</p>"},{"type":"html","id":"ggdiLWnbLl9_text","label":"ggdiLWnbLl9_text","default":"<p>Engineered for creators – discover the features below and claim your deal.</p>"},{"type":"html","id":"gg70CAjuywY_childItem_0","label":"gg70CAjuywY_childItem_0","default":"<p>FEATURE</p>"},{"type":"html","id":"gg70CAjuywY_childItem_1","label":"gg70CAjuywY_childItem_1","default":"<p>WHAT’S INCLUDE</p>"},{"type":"html","id":"gg70CAjuywY_childItem_2","label":"gg70CAjuywY_childItem_2","default":"<p>SPECS</p>"},{"type":"html","id":"ggN5Yk7X3-E_text","label":"ggN5Yk7X3-E_text","default":"<ul><li>Long Model Printing.</li><li>Batch Printing</li><li>Overhang Printing Without Support&nbsp;</li><li>Auto Leveling.</li><li>Upgrade Metal Belt.</li><li>High-Speed Printing with Klipper.</li><li>Remote Monitoring &amp; Control</li><li>Filament Run-Out Detection</li><li>4.3lnch Touch Screen + LED Light</li><li>Open Source Klipper</li><li>250x250x∞mm Printing Size</li><li>400mm/s Printing Speed</li></ul>"},{"type":"html","id":"gg0BpQmRAfa_text","label":"gg0BpQmRAfa_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(34,34,34);font-size:14px;\">IR3 V2 3D printer, 286g Free Filament, USB flash drive, Toolbox, Micro SD, Camera, Quick Start Guide</span></p>"},{"type":"html","id":"ggSOGYd74c5_text","label":"ggSOGYd74c5_text","default":"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."},{"type":"html","id":"ggRcSTlFxGT_label","label":"ggRcSTlFxGT_label","default":"Add to cart"},{"type":"html","id":"ggRcSTlFxGT_outOfStockLabel","label":"ggRcSTlFxGT_outOfStockLabel","default":"Out of stock"},{"type":"html","id":"ggRcSTlFxGT_unavailableLabel","label":"ggRcSTlFxGT_unavailableLabel","default":"Unavailable"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
