# 项目上下文信息

- Playwright MCP存在浏览器实例冲突问题，需要关闭现有实例或使用隔离模式才能正常使用
- Shopify CLI 成功连接配置：使用私有应用访问令牌 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，需要配置 read_themes 和 write_themes 权限，开发主题ID为152864817405，本地开发服务器运行在端口9727
- progress-indicators 没水平居中。card-features元素没展示完整 有一些看不到
- smart-features.liquid 组件的 progress-indicators 元素在桌面端水平居中对齐问题已修复，添加了桌面端专用的居中样式规则
- Playwright MCP存在浏览器实例冲突问题，需要关闭现有实例或使用隔离模式才能正常使用
- 批量打印组件优化需求：1. batch-container容器宽度太小，需要利用更多位置空间 2. 视频是1080*1080正方形分辨率，当前被裁切了 3. 文字素材风格要参考IR3-Hero-Section-1.liquid
- 批量打印组件需要重构：1. 支持交互性质的布局 2. 视频仍然被裁切，需要解决 3. 需要修改布局比例
- IR3 V2批量打印视频组件CSS/JS分离完成：1)移除batch-badge元素 2)创建assets/ir3-batch-printing-video.css(365行样式) 3)创建assets/ir3-batch-printing-video.js(71行脚本) 4)更新liquid文件使用外部引用 5)创建CSS-JS-分离引用方法.md文档 6)测试确认所有功能正常，统一蓝色主题保持完整，组件高度100vh正确
