{% comment %}
  Parameters Display Section for Ideaformer IR3 V2 3D Printer
  File: sections/ir3-parameters-display.liquid
{% endcomment %}

{{ 'ir3-parameters-display.css' | asset_url | stylesheet_tag }}

<section
  class="parameters-section"
  id="parameters-{{ section.id }}"
  style="margin-top: {{ section.settings.margin_top }}px; margin-bottom: {{ section.settings.margin_bottom }}px;"
>
  <!-- Parameters Container -->
  <div class="parameters-container">
    <!-- Enhanced Background Layers -->
    <div class="background-layer">
      <div class="gradient-overlay"></div>
      <div class="animated-gradient"></div>
      <div class="grid-pattern"></div>
      <div class="tech-lines"></div>
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
      </div>
      <div class="particle-field"></div>
    </div>

    <!-- Content Layer -->
    <div class="content-layer">
      <div class="container">
        <!-- Header Section -->
        <div class="parameters-header animate-fade-in" data-delay="0">
          <span class="pre-title animate-slide-in">Technical Specifications</span>
          <h1 class="main-title glitch" data-text="{{ section.settings.main_title | default: 'IR3 V2 Parameters' }}">
            {{ section.settings.main_title | default: 'IR3 V2 Parameters' }}
          </h1>
          <p class="tagline animate-fade-in" data-delay="0.3">
            {{ section.settings.tagline | default: 'Complete technical specifications and capabilities' }}
          </p>
        </div>

        <!-- Tab Navigation -->
        <div class="tab-navigation animate-fade-in" data-delay="0.4">
          <button class="tab-button active" data-tab="machine">
            <span class="tab-icon">⚙️</span>
            <span class="tab-text">Machine Parameters</span>
            <div class="tab-indicator"></div>
          </button>
          <button class="tab-button" data-tab="sensors">
            <span class="tab-icon">📡</span>
            <span class="tab-text">Sensors</span>
            <div class="tab-indicator"></div>
          </button>
          <button class="tab-button" data-tab="electrical">
            <span class="tab-icon">⚡</span>
            <span class="tab-text">Electrical Hardware</span>
            <div class="tab-indicator"></div>
          </button>
          <button class="tab-button" data-tab="software">
            <span class="tab-icon">💻</span>
            <span class="tab-text">Software</span>
            <div class="tab-indicator"></div>
          </button>
        </div>

        <!-- Tab Content -->
        <div class="tab-content-wrapper animate-fade-in" data-delay="0.6">
          <!-- Machine Parameters Tab -->
          <div class="tab-content active" id="machine-tab">
            <div class="parameters-grid">
              <div class="parameter-card">
                <div class="parameter-icon">🏗️</div>
                <div class="parameter-info">
                  <h3>Printing Technology</h3>
                  <p>FDM</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">🔧</div>
                <div class="parameter-info">
                  <h3>Machine Structure</h3>
                  <p>Full metal frame</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">⚡</div>
                <div class="parameter-info">
                  <h3>Motion Structure</h3>
                  <p>CoreXY</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">🎯</div>
                <div class="parameter-info">
                  <h3>Filament Diameter</h3>
                  <p>1.75mm</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">🔩</div>
                <div class="parameter-info">
                  <h3>Motor Type</h3>
                  <p>5:1 Dual gear reduction extruder motor</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">💎</div>
                <div class="parameter-info">
                  <h3>Nozzle Material</h3>
                  <p>Hardened steel</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">📏</div>
                <div class="parameter-info">
                  <h3>Nozzle Size</h3>
                  <p>Standard 0.4mm</p>
                </div>
              </div>
              <div class="parameter-card highlight">
                <div class="parameter-icon">📦</div>
                <div class="parameter-info">
                  <h3>Print Volume</h3>
                  <p>250×250×∞mm (X*Y*Z)</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">📐</div>
                <div class="parameter-info">
                  <h3>Product Dimensions</h3>
                  <p>676×436×510mm</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">📦</div>
                <div class="parameter-info">
                  <h3>Package Dimensions</h3>
                  <p>770×510×320mm</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">⚖️</div>
                <div class="parameter-info">
                  <h3>Net Weight</h3>
                  <p>16.5kg</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">📦</div>
                <div class="parameter-info">
                  <h3>Gross Weight</h3>
                  <p>21kg</p>
                </div>
              </div>
              <div class="parameter-card highlight">
                <div class="parameter-icon">🎯</div>
                <div class="parameter-info">
                  <h3>Print Precision</h3>
                  <p>±0.1mm</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">📏</div>
                <div class="parameter-info">
                  <h3>Layer Thickness</h3>
                  <p>0.1-0.3mm</p>
                </div>
              </div>
              <div class="parameter-card highlight">
                <div class="parameter-icon">🚀</div>
                <div class="parameter-info">
                  <h3>Print Speed</h3>
                  <p>≤400mm/s</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">⚡</div>
                <div class="parameter-info">
                  <h3>Print Acceleration</h3>
                  <p>≤20000mm/s²</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">🔥</div>
                <div class="parameter-info">
                  <h3>Maximum Nozzle Temperature</h3>
                  <p>300°C</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">🔥</div>
                <div class="parameter-info">
                  <h3>Maximum Heated Bed Temperature</h3>
                  <p>90°C</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">⏱️</div>
                <div class="parameter-info">
                  <h3>Nozzle Heating Time</h3>
                  <p>40s</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">⏱️</div>
                <div class="parameter-info">
                  <h3>Heated Bed Heating Time</h3>
                  <p>90s</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">💧</div>
                <div class="parameter-info">
                  <h3>Maximum Flow Rate</h3>
                  <p>26mm³/s</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">🏗️</div>
                <div class="parameter-info">
                  <h3>Print Platform</h3>
                  <p>PEI metal build surface</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">🧪</div>
                <div class="parameter-info">
                  <h3>Compatible Materials</h3>
                  <p>PLA/PETG/TPU/ABS/ASA, etc.</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">🌡️</div>
                <div class="parameter-info">
                  <h3>Operating Environment Temperature</h3>
                  <p>10-40°C</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">🔇</div>
                <div class="parameter-info">
                  <h3>Noise Level</h3>
                  <p>54dB</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">🔗</div>
                <div class="parameter-info">
                  <h3>Print Methods</h3>
                  <p>USB drive/Local network/Internet network</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Sensors Tab -->
          <div class="tab-content" id="sensors-tab">
            <div class="sensors-grid">
              <div class="sensor-card">
                <div class="sensor-status active">✅</div>
                <div class="sensor-info">
                  <h3>Vibration Compensation</h3>
                  <p>Advanced vibration detection and compensation system</p>
                </div>
              </div>
              <div class="sensor-card">
                <div class="sensor-status active">✅</div>
                <div class="sensor-info">
                  <h3>Filament Runout Detection</h3>
                  <p>Automatic detection when filament runs out</p>
                </div>
              </div>
              <div class="sensor-card">
                <div class="sensor-status active">✅</div>
                <div class="sensor-info">
                  <h3>Material Shortage Detection</h3>
                  <p>Smart monitoring of material levels</p>
                </div>
              </div>
              <div class="sensor-card">
                <div class="sensor-status active">✅</div>
                <div class="sensor-info">
                  <h3>Clogging Detection</h3>
                  <p>Real-time nozzle clogging detection</p>
                </div>
              </div>
              <div class="sensor-card">
                <div class="sensor-status active">✅</div>
                <div class="sensor-info">
                  <h3>Auto Leveling</h3>
                  <p>Automatic bed leveling system</p>
                </div>
              </div>
              <div class="sensor-card">
                <div class="sensor-status active">✅</div>
                <div class="sensor-info">
                  <h3>LED Lighting</h3>
                  <p>Built-in LED lighting system</p>
                </div>
              </div>
              <div class="sensor-card">
                <div class="sensor-status active">✅</div>
                <div class="sensor-info">
                  <h3>Camera</h3>
                  <p>Integrated camera for monitoring</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Electrical Hardware Tab -->
          <div class="tab-content" id="electrical-tab">
            <div class="parameters-grid">
              <div class="parameter-card">
                <div class="parameter-icon">⚡</div>
                <div class="parameter-info">
                  <h3>Input Voltage</h3>
                  <p>110VAC/220VAC, 50/60Hz</p>
                </div>
              </div>
              <div class="parameter-card highlight">
                <div class="parameter-icon">🔋</div>
                <div class="parameter-info">
                  <h3>Maximum Power</h3>
                  <p>800W</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">🖥️</div>
                <div class="parameter-info">
                  <h3>Main Controller</h3>
                  <p>64-bit 1.5GHz Quad-core Cortex-A53 processor</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">💾</div>
                <div class="parameter-info">
                  <h3>Memory</h3>
                  <p>16GB-SD, 1GB DDR3</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">📱</div>
                <div class="parameter-info">
                  <h3>User Interface</h3>
                  <p>4.3-inch touchscreen with 800×480 resolution</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">⚙️</div>
                <div class="parameter-info">
                  <h3>Printing Firmware</h3>
                  <p>Klipper</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Software Tab -->
          <div class="tab-content" id="software-tab">
            <div class="parameters-grid">
              <div class="parameter-card">
                <div class="parameter-icon">🔧</div>
                <div class="parameter-info">
                  <h3>Slicing Software</h3>
                  <p>Ideamaker/Ideaformer Cura</p>
                </div>
              </div>
              <div class="parameter-card highlight">
                <div class="parameter-icon">📁</div>
                <div class="parameter-info">
                  <h3>Input Formats</h3>
                  <p>.stl/.obj/.3mf/.step/.stp/.iges/.igs/.oltp/.jpg/.jpeg/.png/.bmp</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">💻</div>
                <div class="parameter-info">
                  <h3>Operating Systems</h3>
                  <p>Windows/MacOS/Linux</p>
                </div>
              </div>
              <div class="parameter-card">
                <div class="parameter-icon">📄</div>
                <div class="parameter-info">
                  <h3>Output File Format</h3>
                  <p>.gcode</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

{{ 'ir3-parameters-display.js' | asset_url | script_tag }}

{% schema %}
{
  "name": "IR3 Parameters",
  "tag": "section",
  "class": "parameters-display-section",
  "settings": [
    {
      "type": "header",
      "content": "Content Settings"
    },
    {
      "type": "text",
      "id": "main_title",
      "label": "Main Title",
      "default": "IR3 V2 Parameters"
    },
    {
      "type": "textarea",
      "id": "tagline",
      "label": "Tagline",
      "default": "Complete technical specifications and capabilities"
    },
    {
      "type": "header",
      "content": "Spacing Settings"
    },
    {
      "type": "number",
      "id": "margin_top",
      "label": "Top Margin (px)",
      "default": 0,
      "info": "输入精确的上边距像素值"
    },
    {
      "type": "number",
      "id": "margin_bottom",
      "label": "Bottom Margin (px)",
      "default": 0,
      "info": "输入精确的下边距像素值"
    }
  ],
  "presets": [
    {
      "name": "IR3 Parameters"
    }
  ]
}
{% endschema %}